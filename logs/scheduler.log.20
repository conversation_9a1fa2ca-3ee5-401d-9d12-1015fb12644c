2025-06-10 19:02:31,467 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 19:02:31,471 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 19:02:31,476 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:02:31,487 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 19:02:31,490 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 19:02:31,494 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 19:02:31,568 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-10 19:02:31,571 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-10 19:02:31,574 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-10 19:02:31,577 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-10 19:02:31,581 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-10 19:02:31,586 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 19:02:31,629 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-10 19:02:31,631 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-10 19:02:31,636 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-10 19:02:31,639 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-10 19:02:31,643 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-10 19:02:31,646 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-10 19:02:31,650 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-10 19:02:31,653 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:02:31,657 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:02:31,661 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 19:02:31,664 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 19:02:31,667 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 19:02:31,669 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 19:03:32,771 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 19:03:32,776 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 19:03:32,786 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:03:32,806 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 19:03:32,812 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 19:03:32,817 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 19:03:32,910 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-10 19:03:32,913 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-10 19:03:32,916 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-10 19:03:32,918 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-10 19:03:32,923 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-10 19:03:32,928 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 19:03:32,980 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-10 19:03:32,984 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-10 19:03:32,987 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-10 19:03:32,990 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-10 19:03:32,993 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-10 19:03:32,996 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-10 19:03:32,999 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-10 19:03:33,003 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:03:33,006 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:03:33,012 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 19:03:33,015 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 19:03:33,019 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 19:03:33,022 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 19:04:33,769 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 19:04:33,772 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 19:04:33,774 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 19:04:33,777 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:04:33,787 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 19:04:33,790 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 19:04:33,792 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 19:04:33,804 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-10 19:04:33,861 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-10 19:04:33,864 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-10 19:04:33,866 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-10 19:04:33,869 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-10 19:04:33,873 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-10 19:04:33,878 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 19:04:33,925 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-10 19:04:33,928 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-10 19:04:33,930 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-10 19:04:33,933 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-10 19:04:33,938 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-10 19:04:33,940 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-10 19:04:33,943 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-10 19:04:33,946 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:04:33,949 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:04:33,953 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 19:04:33,956 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 19:04:33,958 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 19:04:33,960 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 19:05:34,124 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 19:06:35,709 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 19:06:35,751 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-10 19:07:36,514 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 19:08:37,902 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 19:08:37,931 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-10 19:08:37,937 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-10 19:09:39,228 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 19:09:39,231 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:09:39,261 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-10 19:09:39,269 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-10 19:09:39,392 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:09:39,394 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:13:42,833 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:13:43,085 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:13:43,090 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:14:44,593 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:14:44,758 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:14:44,761 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:15:45,585 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:15:45,733 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:15:45,738 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:25:55,878 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:25:56,032 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:25:56,035 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:29:59,464 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:29:59,648 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:29:59,651 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:31:01,035 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:31:01,182 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:31:01,184 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:33:02,855 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:33:02,993 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:33:02,995 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:33:02,999 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 19:34:03,630 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:34:03,777 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:34:03,780 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:34:03,784 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 19:35:04,665 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:35:04,823 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:35:04,825 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:35:04,829 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 19:36:05,105 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 19:36:05,232 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 19:36:05,234 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 19:36:05,238 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 20:41:19,243 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 20:41:19,425 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 20:41:19,428 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 20:41:19,434 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 20:42:20,919 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 20:42:21,181 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 20:42:21,186 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 20:42:21,196 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 20:45:23,800 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 20:45:23,996 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 20:45:23,999 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 20:46:25,618 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 20:46:25,622 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 20:46:25,627 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 20:46:25,638 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 20:46:25,642 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 20:46:25,645 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 20:46:25,753 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 20:46:25,823 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 20:46:25,826 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 20:46:25,834 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 20:46:25,837 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 20:46:25,840 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 20:47:26,662 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 20:47:26,667 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 20:47:26,677 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 20:47:26,700 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 20:47:26,705 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 20:47:26,710 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 20:47:26,873 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 20:47:26,953 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 20:47:26,956 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 20:47:26,966 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 20:47:26,970 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 20:47:26,975 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 20:51:31,528 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 20:52:33,222 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 20:54:35,640 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 20:55:37,124 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 20:56:38,740 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 20:57:39,092 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 20:57:39,283 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 20:57:39,287 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 20:57:39,293 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 21:01:44,098 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 21:01:44,104 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 21:01:44,108 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:01:44,118 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 21:01:44,122 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 21:01:44,124 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 21:01:44,224 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-10 21:01:44,227 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-10 21:01:44,231 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-10 21:01:44,234 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-10 21:01:44,240 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-10 21:01:44,246 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 21:01:44,303 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-10 21:01:44,306 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-10 21:01:44,311 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-10 21:01:44,315 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-10 21:01:44,318 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-10 21:01:44,322 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-10 21:01:44,325 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-10 21:01:44,329 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:01:44,333 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:01:44,339 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 21:01:44,343 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 21:01:44,346 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 21:01:44,350 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 21:02:46,036 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 21:02:46,039 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 21:02:46,047 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:02:46,062 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 21:02:46,066 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 21:02:46,071 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 21:02:46,232 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-10 21:02:46,238 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-10 21:02:46,244 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-10 21:02:46,248 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-10 21:02:46,255 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-10 21:02:46,261 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 21:02:46,327 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-10 21:02:46,330 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-10 21:02:46,334 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-10 21:02:46,338 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-10 21:02:46,342 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-10 21:02:46,347 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-10 21:02:46,352 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-10 21:02:46,357 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:02:46,362 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:02:46,368 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 21:02:46,372 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 21:02:46,375 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 21:02:46,379 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 21:04:48,548 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 21:05:50,062 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:05:50,108 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-10 21:05:50,262 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:05:50,265 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:06:50,829 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 21:06:50,832 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:06:50,857 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-10 21:06:51,035 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:06:51,039 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:07:52,329 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 21:07:52,333 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:07:52,364 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-10 21:07:52,372 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-06-10 21:07:52,595 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:07:52,599 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:46:39,652 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 21:46:39,657 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 21:46:39,672 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 21:46:39,674 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 21:46:39,676 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 21:46:39,755 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 21:46:39,823 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 21:46:39,826 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 21:46:39,829 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 21:47:41,030 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 21:47:41,033 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 21:47:41,061 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 21:47:41,064 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 21:47:41,067 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 21:47:41,196 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 21:47:41,281 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 21:47:41,285 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 21:47:41,288 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 21:48:42,295 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 21:48:42,299 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 21:48:42,322 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 21:48:42,326 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 21:48:42,329 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 21:48:42,415 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 21:48:42,485 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 21:48:42,487 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 21:48:42,489 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 21:49:42,740 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 21:49:42,743 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 21:49:42,747 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:49:42,756 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 21:49:42,758 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 21:49:42,760 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 21:49:42,841 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 21:49:42,900 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:49:42,902 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:49:42,909 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 21:49:42,911 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 21:49:42,914 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 21:51:45,421 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 21:52:46,885 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 21:53:47,351 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:53:47,571 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:53:47,576 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:53:47,583 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 21:54:49,233 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:54:49,431 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:54:49,434 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:54:49,440 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 21:57:52,325 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:57:52,598 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:57:52,602 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:58:53,079 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:58:53,312 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:58:53,315 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 21:59:55,036 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 21:59:55,199 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 21:59:55,202 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:01:56,913 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 22:01:56,916 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 22:01:56,931 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 22:01:56,934 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 22:01:56,937 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 22:01:57,071 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-10 22:01:57,073 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-10 22:01:57,076 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-10 22:01:57,078 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-10 22:01:57,082 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-10 22:01:57,086 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 22:01:57,132 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-10 22:01:57,135 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-10 22:01:57,138 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-10 22:01:57,140 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-10 22:01:57,143 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-10 22:01:57,145 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-10 22:01:57,148 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-10 22:01:57,156 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 22:01:57,159 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 22:01:57,162 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 22:01:57,164 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 22:02:58,345 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 22:02:58,348 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 22:02:58,363 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 22:02:58,365 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 22:02:58,367 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 22:02:58,377 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-06-10 22:02:58,433 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-10 22:02:58,435 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-10 22:02:58,437 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-10 22:02:58,440 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-10 22:02:58,444 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-10 22:02:58,448 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 22:02:58,493 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-10 22:02:58,495 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-10 22:02:58,498 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-10 22:02:58,500 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-10 22:02:58,503 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-10 22:02:58,506 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-10 22:02:58,508 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-10 22:02:58,517 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 22:02:58,520 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 22:02:58,523 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 22:02:58,526 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 22:03:59,134 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 22:03:59,138 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 22:03:59,152 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 22:03:59,155 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 22:03:59,157 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 22:03:59,228 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-10 22:03:59,230 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-10 22:03:59,233 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-10 22:03:59,236 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-10 22:03:59,240 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-10 22:03:59,245 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 22:03:59,292 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-10 22:03:59,295 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-10 22:03:59,297 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-10 22:03:59,300 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-10 22:03:59,303 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-10 22:03:59,305 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-10 22:03:59,308 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-10 22:03:59,317 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 22:03:59,319 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 22:03:59,322 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 22:03:59,325 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 22:05:00,755 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for child_ngo
2025-06-10 22:05:00,759 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for child_ngo
2025-06-10 22:05:00,762 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-06-10 22:05:00,785 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for child_ngo
2025-06-10 22:05:00,789 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for child_ngo
2025-06-10 22:05:00,793 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for child_ngo
2025-06-10 22:05:00,875 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for child_ngo
2025-06-10 22:05:00,878 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for child_ngo
2025-06-10 22:05:00,881 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for child_ngo
2025-06-10 22:05:00,883 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for child_ngo
2025-06-10 22:05:00,888 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for child_ngo
2025-06-10 22:05:00,893 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for child_ngo
2025-06-10 22:05:00,950 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-06-10 22:05:00,954 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for child_ngo
2025-06-10 22:05:00,959 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for child_ngo
2025-06-10 22:05:00,963 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-06-10 22:05:00,966 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-06-10 22:05:00,969 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-06-10 22:05:00,972 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for child_ngo
2025-06-10 22:05:00,984 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for child_ngo
2025-06-10 22:05:00,988 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-06-10 22:05:00,991 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-06-10 22:05:00,994 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-06-10 22:33:33,795 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:33:33,942 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:33:33,945 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:34:34,471 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:34:34,644 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:34:34,647 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:35:35,558 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:35:35,734 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:35:35,737 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:37:37,604 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:37:37,751 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:37:37,754 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:38:38,504 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:38:38,684 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:38:38,688 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:39:39,603 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:39:39,769 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:39:39,772 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:40:41,227 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:40:41,391 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:40:41,393 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:45:45,583 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:45:45,738 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:45:45,742 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:49:49,894 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:49:50,061 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:49:50,063 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
2025-06-10 22:50:51,360 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for child_ngo
2025-06-10 22:50:51,542 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for child_ngo
2025-06-10 22:50:51,545 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for child_ngo
