2025-08-01 00:01:52,916 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-08-01 00:01:52,926 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-08-01 00:01:52,929 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-08-01 00:01:52,932 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-08-01 00:01:52,936 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-08-01 00:01:52,938 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-08-01 00:01:52,942 ERROR scheduler Skipped queueing erpnext.accounts.deferred_revenue.process_deferred_accounting because it was found in queue for parentngo
2025-08-01 00:01:52,944 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-08-01 00:01:52,952 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-08-01 00:01:52,956 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-08-01 00:01:52,959 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-08-01 00:01:52,968 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-08-01 00:01:52,970 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-08-01 00:01:52,978 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-08-01 00:01:52,980 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-08-01 00:01:52,986 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-08-01 00:01:52,991 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-08-01 00:01:52,993 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-08-01 00:01:53,007 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-08-01 00:01:53,010 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-08-01 00:01:53,016 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-08-01 00:01:53,019 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-08-01 00:01:53,023 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-08-01 00:01:53,033 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for parentngo
2025-08-01 00:01:53,043 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-08-01 00:01:53,045 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-08-01 00:01:53,047 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-08-01 00:01:53,049 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-08-01 00:01:53,054 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-08-01 00:01:53,057 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-08-01 00:01:53,059 ERROR scheduler Skipped queueing erpnext.accounts.utils.auto_create_exchange_rate_revaluation_monthly because it was found in queue for parentngo
2025-08-03 11:03:06,465 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-08-03 11:03:06,469 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-08-03 11:03:06,472 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-08-03 11:03:06,474 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-08-03 11:03:06,477 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for serviceplanner
2025-08-03 11:03:06,482 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-08-03 11:03:06,487 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-08-03 11:03:06,491 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-08-03 11:03:06,493 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-08-03 11:03:06,496 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for serviceplanner
2025-08-03 11:03:06,498 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-08-03 11:03:06,502 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-08-03 11:03:06,505 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-08-03 11:03:06,513 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-08-03 11:03:06,515 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-08-03 11:03:06,520 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-08-03 11:03:06,523 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-08-03 11:03:06,524 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-08-03 11:03:06,528 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-08-03 11:03:06,537 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-08-03 11:03:06,541 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-08-03 11:03:06,544 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-08-03 11:03:06,546 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-08-03 11:03:06,548 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-08-03 11:03:06,552 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-08-03 11:03:06,553 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-08-03 11:03:06,556 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-08-03 11:03:06,557 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for serviceplanner
2025-08-03 11:03:06,566 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-08-03 11:03:06,568 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-08-03 11:03:06,570 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for serviceplanner
2025-08-03 11:03:06,572 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-08-03 11:03:06,574 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for serviceplanner
2025-08-03 11:03:06,576 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-08-03 11:03:06,814 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for theme.com
2025-08-03 11:03:06,822 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for theme.com
2025-08-03 11:03:06,836 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for theme.com
2025-08-03 11:03:06,844 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for theme.com
2025-08-03 11:03:06,847 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for theme.com
2025-08-03 11:03:06,853 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for theme.com
2025-08-03 11:03:06,857 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for theme.com
2025-08-03 11:03:06,865 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for theme.com
2025-08-03 11:03:06,867 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for theme.com
2025-08-03 11:03:06,869 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for theme.com
2025-08-03 11:03:06,878 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for theme.com
2025-08-03 11:03:06,883 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for theme.com
2025-08-03 11:03:06,889 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for theme.com
2025-08-03 11:03:06,897 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for theme.com
2025-08-03 11:03:06,905 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for theme.com
2025-08-03 11:03:06,910 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for theme.com
2025-08-04 11:35:09,682 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-08-04 11:35:09,690 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-08-04 11:35:09,692 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-08-04 11:35:09,696 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-08-04 11:35:09,698 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-08-04 11:35:09,700 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-08-04 11:35:09,703 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-08-04 11:35:09,705 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-08-04 11:35:09,711 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-08-04 11:35:09,721 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-08-04 11:35:09,723 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-08-04 11:35:09,726 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-08-04 11:35:09,728 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-08-04 11:35:09,739 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-08-04 11:35:09,762 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-08-04 11:35:09,764 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-08-04 11:35:09,770 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-08-04 11:35:09,773 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-08-04 11:35:09,780 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-08-04 11:35:09,782 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-08-04 11:35:09,784 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-08-04 11:35:09,789 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-08-04 11:35:09,792 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-08-04 11:35:09,796 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-08-04 11:35:09,798 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-08-04 11:35:09,800 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-08-04 11:35:09,807 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-08-04 11:35:09,817 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-08-04 11:35:10,413 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-08-04 11:35:10,415 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-08-04 11:35:10,418 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-08-04 11:35:10,428 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-08-04 11:35:10,431 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-08-04 11:35:10,433 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for testmahmoud
2025-08-04 11:35:10,449 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-08-04 11:35:10,455 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-08-04 11:35:10,468 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for testmahmoud
2025-08-04 11:35:10,473 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for testmahmoud
2025-08-04 11:35:10,476 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for testmahmoud
2025-08-04 11:35:10,479 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-08-04 11:35:10,481 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-08-04 11:35:10,483 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-08-04 11:35:10,494 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-08-04 11:35:10,502 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-08-04 11:35:10,509 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-08-04 11:35:10,520 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-08-04 11:35:10,522 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-08-04 11:35:10,527 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for testmahmoud
2025-08-04 11:35:10,530 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-08-04 11:35:10,533 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-08-04 11:35:10,542 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-08-04 11:35:10,559 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-08-04 11:35:10,561 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-08-04 11:35:10,564 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for testmahmoud
2025-08-04 11:35:10,569 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for testmahmoud
2025-08-04 11:35:10,584 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-08-04 11:35:10,590 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-08-04 11:35:10,592 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-08-04 11:35:10,599 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-08-04 11:35:10,601 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-08-04 11:35:10,604 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-08-04 11:35:10,609 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-08-04 11:35:10,612 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-08-04 11:35:10,619 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-08-04 11:35:10,621 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-08-04 11:35:10,624 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-08-04 11:35:10,626 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-08-04 11:35:10,628 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-08-04 11:35:10,630 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-08-04 11:35:10,635 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-08-04 11:35:10,637 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-08-04 11:35:10,643 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-08-04 11:35:10,645 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-08-04 11:35:10,649 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-08-04 11:35:10,652 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-08-04 11:35:10,664 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-08-04 11:35:10,666 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-08-04 11:35:10,669 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-08-04 11:35:10,675 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-08-04 11:35:10,693 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-08-04 11:35:10,701 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-08-04 11:35:10,705 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-08-04 11:35:10,731 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for featuretracker
2025-08-04 11:35:10,737 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for featuretracker
2025-08-04 11:35:10,739 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for featuretracker
2025-08-04 11:35:10,745 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for featuretracker
2025-08-04 11:35:10,755 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for featuretracker
2025-08-04 11:35:10,757 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for featuretracker
2025-08-04 11:35:10,765 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for featuretracker
2025-08-04 11:35:10,768 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for featuretracker
2025-08-04 11:35:10,772 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for featuretracker
2025-08-04 11:35:10,773 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for featuretracker
2025-08-04 11:35:10,779 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for featuretracker
2025-08-04 11:35:10,781 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for featuretracker
2025-08-05 14:38:00,977 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-08-05 14:38:00,989 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-08-05 14:38:00,991 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-08-05 14:38:00,996 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-08-05 14:38:00,998 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-08-05 14:38:01,000 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-08-05 14:38:01,003 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-08-05 14:38:01,007 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-08-05 14:38:01,016 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-08-05 14:38:01,020 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-08-05 14:38:01,033 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-08-05 14:38:01,042 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-08-05 14:38:01,049 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-08-05 14:38:01,062 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-08-05 14:38:01,065 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-08-05 14:38:01,071 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-08-05 14:38:01,074 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-08-05 14:38:01,077 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-08-05 14:38:01,086 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-08-05 14:38:01,099 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-08-05 14:38:01,105 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-08-05 14:38:01,112 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-08-05 14:38:01,119 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-08-05 14:38:01,123 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-08-05 14:38:01,152 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-08-05 14:38:01,158 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-08-05 14:38:01,173 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-08-05 14:38:01,184 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-08-05 14:38:01,206 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for nexus.com
2025-08-05 14:38:01,226 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for nexus.com
2025-08-05 14:38:01,257 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for nexus.com
2025-08-05 14:38:01,266 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for nexus.com
2025-08-05 14:38:01,345 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for nexus.com
2025-08-05 14:38:01,366 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for nexus.com
2025-08-05 14:38:01,368 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for nexus.com
2025-08-05 14:38:01,370 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for nexus.com
2025-08-05 14:38:01,554 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-08-05 14:38:01,561 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for child_ngo
2025-08-05 14:38:01,563 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for child_ngo
2025-08-05 14:38:01,572 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for child_ngo
2025-08-05 14:38:01,575 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-08-05 14:38:01,580 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for child_ngo
2025-08-05 14:38:01,589 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-08-05 14:38:01,595 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for child_ngo
2025-08-05 14:38:01,612 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-08-05 14:38:01,626 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-08-05 14:38:01,632 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for child_ngo
2025-08-05 14:38:01,635 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-08-05 14:38:01,639 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for child_ngo
2025-08-05 14:38:01,647 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for child_ngo
2025-08-05 14:38:01,650 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for child_ngo
2025-08-05 14:38:01,668 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for child_ngo
2025-08-05 14:38:01,673 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-08-05 14:38:01,677 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for child_ngo
2025-08-05 14:38:01,681 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for child_ngo
2025-08-05 14:38:01,692 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-08-05 14:38:01,694 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for child_ngo
2025-08-05 14:38:01,697 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for child_ngo
2025-08-05 14:38:01,706 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-08-05 14:38:01,723 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for child_ngo
2025-08-05 14:38:01,736 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for child_ngo
2025-08-05 14:38:01,742 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for child_ngo
2025-08-05 14:38:01,745 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for child_ngo
2025-08-05 14:38:01,754 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-08-05 14:38:01,780 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for theme.com
2025-08-05 14:38:01,787 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for theme.com
2025-08-05 14:38:01,814 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for theme.com
2025-08-05 14:38:01,841 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for theme.com
2025-08-05 14:38:01,845 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for theme.com
2025-08-05 14:38:01,864 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for theme.com
2025-08-05 14:38:01,873 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for theme.com
2025-08-05 14:38:01,878 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for theme.com
2025-08-05 14:38:01,888 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for theme.com
2025-08-05 14:38:01,892 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for theme.com
2025-08-05 14:38:01,898 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for theme.com
2025-08-05 14:38:01,900 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for theme.com
2025-08-05 14:38:02,087 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for featuretracker
2025-08-05 14:38:02,092 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for featuretracker
2025-08-05 14:38:02,094 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for featuretracker
2025-08-05 14:38:02,097 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for featuretracker
2025-08-05 14:38:02,099 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for featuretracker
2025-08-05 14:38:02,104 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for featuretracker
2025-08-05 14:38:02,106 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for featuretracker
2025-08-05 14:38:02,108 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for featuretracker
2025-08-05 14:38:02,112 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for featuretracker
2025-08-05 14:38:02,114 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for featuretracker
2025-08-05 14:38:02,118 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for featuretracker
2025-08-05 14:38:02,119 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for featuretracker
2025-08-05 14:38:02,121 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for featuretracker
2025-08-05 14:38:02,124 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for featuretracker
2025-08-05 14:38:02,132 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for featuretracker
2025-08-05 14:38:02,145 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for featuretracker
2025-08-05 14:38:02,149 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for featuretracker
2025-08-05 14:38:02,153 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for featuretracker
2025-08-05 14:38:02,155 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for featuretracker
2025-08-05 14:38:02,160 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for featuretracker
2025-08-06 10:59:15,492 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for featuretracker
2025-08-06 10:59:15,497 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for featuretracker
2025-08-06 10:59:15,500 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for featuretracker
2025-08-06 10:59:15,503 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for featuretracker
2025-08-06 10:59:15,511 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for featuretracker
2025-08-06 10:59:15,516 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for featuretracker
2025-08-06 10:59:15,526 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for featuretracker
2025-08-06 10:59:15,528 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for featuretracker
2025-08-06 10:59:15,531 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for featuretracker
2025-08-06 10:59:15,544 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for featuretracker
2025-08-06 10:59:15,557 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for featuretracker
2025-08-06 10:59:15,560 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for featuretracker
2025-08-06 10:59:15,566 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for featuretracker
2025-08-06 10:59:15,574 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for featuretracker
2025-08-06 10:59:15,580 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for featuretracker
2025-08-06 10:59:15,583 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for featuretracker
2025-08-06 10:59:15,585 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for featuretracker
2025-08-06 10:59:15,588 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for featuretracker
2025-08-06 10:59:15,594 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for featuretracker
2025-08-06 10:59:15,598 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for featuretracker
2025-08-06 10:59:15,676 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-08-06 10:59:15,681 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-08-06 10:59:15,718 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-08-06 10:59:15,723 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-08-06 10:59:15,745 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-08-06 10:59:15,776 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-08-06 10:59:15,783 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-08-06 10:59:15,801 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-08-06 15:01:38,207 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for featuretracker
2025-08-06 15:01:38,217 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for featuretracker
2025-08-06 15:01:38,222 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for featuretracker
2025-08-06 15:01:38,241 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for featuretracker
2025-08-06 15:01:38,248 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for featuretracker
2025-08-06 15:01:38,254 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for featuretracker
2025-08-06 15:01:38,260 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for featuretracker
2025-08-06 15:01:38,266 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for featuretracker
2025-08-06 15:01:38,273 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for featuretracker
2025-08-06 15:01:38,283 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for featuretracker
2025-08-06 15:01:38,300 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for featuretracker
2025-08-06 15:01:38,304 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for featuretracker
2025-08-06 15:01:38,311 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for featuretracker
2025-08-06 15:01:38,322 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for featuretracker
2025-08-06 15:01:38,335 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for featuretracker
2025-08-06 15:01:38,339 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for featuretracker
2025-08-06 15:01:38,344 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for featuretracker
2025-08-06 15:01:38,348 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for featuretracker
2025-08-06 15:01:38,355 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for featuretracker
2025-08-06 15:01:38,570 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for theme.com
2025-08-06 15:01:38,590 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for theme.com
2025-08-06 15:01:38,760 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for nexus.com
2025-08-06 15:01:38,773 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for nexus.com
2025-08-06 15:01:38,820 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for nexus.com
2025-08-06 15:01:38,841 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for nexus.com
2025-08-06 15:01:38,856 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for nexus.com
2025-08-06 15:01:38,880 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for nexus.com
2025-08-06 15:01:38,893 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for nexus.com
2025-08-06 15:01:38,900 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for nexus.com
2025-08-06 15:01:38,904 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for nexus.com
2025-08-06 15:01:38,921 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for nexus.com
2025-08-06 15:01:38,925 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for nexus.com
2025-08-06 15:01:38,935 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for nexus.com
2025-08-06 15:01:38,973 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for nexus.com
2025-08-06 15:01:38,982 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for nexus.com
2025-08-06 15:01:39,000 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for nexus.com
2025-08-06 15:01:39,030 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for nexus.com
2025-08-06 15:01:39,041 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for nexus.com
2025-08-06 15:01:39,063 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for nexus.com
2025-08-06 15:01:39,082 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for nexus.com
2025-08-06 15:01:39,087 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for nexus.com
2025-08-06 15:01:39,103 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for nexus.com
2025-08-06 15:01:39,109 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for nexus.com
2025-08-06 15:01:39,117 ERROR scheduler Skipped queueing frappe.email.queue.retry_sending_emails because it was found in queue for nexus.com
2025-08-06 15:01:39,135 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for nexus.com
2025-08-06 15:01:39,159 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for nexus.com
2025-08-06 15:01:39,165 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for nexus.com
2025-08-06 15:01:39,170 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for nexus.com
2025-08-06 15:01:39,208 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for nexus.com
2025-08-06 15:01:39,272 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,283 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,291 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,297 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,304 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,317 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,329 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,335 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,339 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,348 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,358 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,362 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,377 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,387 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,395 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,414 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,431 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,441 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,451 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,460 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for brainwise.helpdesk
2025-08-06 15:01:39,578 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-08-06 15:01:39,617 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-08-06 15:01:39,636 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-08-06 15:01:39,642 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-08-06 15:01:39,654 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-08-06 15:01:39,688 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-08-06 15:01:39,700 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-08-06 15:01:39,712 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-08-06 15:01:39,738 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-08-06 15:01:39,749 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-08-06 15:01:39,867 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-08-06 15:01:39,992 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for child_ngo
2025-08-06 15:01:40,103 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for child_ngo
2025-08-06 15:01:40,142 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for child_ngo
2025-08-06 15:01:40,168 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-08-06 15:01:40,172 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for child_ngo
2025-08-06 15:01:40,177 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for child_ngo
2025-08-06 15:01:40,304 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for child_ngo
2025-08-06 15:01:40,308 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for child_ngo
2025-08-06 15:01:40,315 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for child_ngo
2025-08-06 15:01:40,333 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for child_ngo
2025-08-06 15:01:40,421 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-08-06 15:01:40,425 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-08-06 15:01:40,481 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-08-06 15:01:40,532 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-08-06 15:01:40,548 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-08-06 15:01:40,579 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-08-06 15:01:40,584 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-08-06 15:01:40,592 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-08-06 15:01:40,623 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-08-06 15:01:40,627 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-08-06 15:01:40,759 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-08-06 15:01:40,802 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for serviceplanner
2025-08-06 15:01:40,834 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-08-06 15:01:40,879 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for serviceplanner
2025-08-06 15:01:40,891 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-08-06 15:01:40,904 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-08-06 15:01:40,932 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-08-06 15:01:40,974 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-08-06 15:01:41,036 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-08-06 15:01:41,058 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for serviceplanner
2025-08-06 15:01:41,066 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for serviceplanner
2025-08-06 15:01:41,078 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-08-06 15:01:41,101 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-08-06 15:01:41,116 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-08-06 15:01:41,121 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for serviceplanner
2025-08-06 15:01:41,219 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for customhr
2025-08-06 15:01:41,416 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for customhr
2025-08-07 12:26:52,621 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for child_ngo
2025-08-10 11:52:13,686 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,709 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,712 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,714 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,717 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,720 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,722 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,726 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,730 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,732 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,737 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,739 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,753 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,755 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,759 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,761 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,766 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for brainwise.helpdesk
2025-08-10 11:52:13,787 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for parentngo
2025-08-10 11:52:13,789 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for parentngo
2025-08-10 11:52:13,792 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for parentngo
2025-08-10 11:52:13,794 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for parentngo
2025-08-10 11:52:13,799 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for parentngo
2025-08-10 11:52:13,801 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for parentngo
2025-08-10 11:52:13,806 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for parentngo
2025-08-10 11:52:13,808 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for parentngo
2025-08-10 11:52:13,815 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for parentngo
2025-08-10 11:52:13,820 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for parentngo
2025-08-10 11:52:13,830 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for parentngo
2025-08-10 11:52:13,832 ERROR scheduler Skipped queueing license_manager_client.crons.license.validate_license_hourly because it was found in queue for parentngo
2025-08-10 11:52:13,834 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for parentngo
2025-08-10 11:52:13,839 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for parentngo
2025-08-10 11:52:13,842 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for parentngo
2025-08-10 11:52:13,846 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for parentngo
2025-08-10 11:52:13,849 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for parentngo
2025-08-10 11:52:13,853 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for parentngo
2025-08-10 11:52:13,856 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for parentngo
2025-08-10 11:52:13,860 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for parentngo
2025-08-10 11:52:13,862 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for parentngo
2025-08-10 11:52:13,875 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for parentngo
2025-08-10 11:52:13,880 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for parentngo
2025-08-10 11:52:13,882 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for parentngo
2025-08-10 11:52:13,886 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for parentngo
2025-08-10 11:52:13,888 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for parentngo
2025-08-10 11:52:13,890 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for parentngo
2025-08-10 11:52:13,893 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for parentngo
2025-08-10 11:52:13,907 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for parentngo
2025-08-10 11:52:13,920 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for parentngo
2025-08-10 11:52:13,924 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for parentngo
2025-08-10 11:52:13,932 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for parentngo
2025-08-10 11:52:13,934 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for parentngo
2025-08-10 11:52:13,939 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for parentngo
2025-08-10 11:52:13,946 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for parentngo
2025-08-10 11:52:14,672 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for serviceplanner
2025-08-10 11:52:14,676 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for serviceplanner
2025-08-10 11:52:14,678 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for serviceplanner
2025-08-10 11:52:14,682 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for serviceplanner
2025-08-10 11:52:14,683 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for serviceplanner
2025-08-10 11:52:14,686 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for serviceplanner
2025-08-10 11:52:14,689 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for serviceplanner
2025-08-10 11:52:14,692 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for serviceplanner
2025-08-10 11:52:14,694 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for serviceplanner
2025-08-10 11:52:14,703 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for serviceplanner
2025-08-10 11:52:14,706 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for serviceplanner
2025-08-10 11:52:14,708 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for serviceplanner
2025-08-10 11:52:14,709 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for serviceplanner
2025-08-10 11:52:14,711 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-08-10 11:52:14,719 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for serviceplanner
2025-08-10 11:52:14,721 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for serviceplanner
2025-08-10 11:52:14,723 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for serviceplanner
2025-08-10 11:52:14,726 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for serviceplanner
2025-08-10 11:52:14,729 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for serviceplanner
2025-08-10 11:52:14,736 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for serviceplanner
2025-08-10 11:52:14,738 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for serviceplanner
2025-08-10 11:52:14,742 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for serviceplanner
2025-08-10 11:52:14,751 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for serviceplanner
2025-08-10 11:52:14,756 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for serviceplanner
2025-08-10 11:52:14,765 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for serviceplanner
2025-08-10 11:52:14,767 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for serviceplanner
2025-08-10 11:52:14,768 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for serviceplanner
2025-08-10 11:52:14,771 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for serviceplanner
2025-08-10 11:52:14,773 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for serviceplanner
2025-08-10 11:52:14,776 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for serviceplanner
2025-08-10 11:52:14,781 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for serviceplanner
2025-08-10 11:52:14,783 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for serviceplanner
2025-08-10 11:52:14,787 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for serviceplanner
2025-08-10 11:52:14,789 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for serviceplanner
2025-08-10 11:52:14,812 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for testmahmoud
2025-08-10 11:52:14,815 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for testmahmoud
2025-08-10 11:52:14,817 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for testmahmoud
2025-08-10 11:52:14,818 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-08-10 11:52:14,820 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for testmahmoud
2025-08-10 11:52:14,822 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-08-10 11:52:14,824 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for testmahmoud
2025-08-10 11:52:14,827 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for testmahmoud
2025-08-10 11:52:14,834 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-08-10 11:52:14,839 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for testmahmoud
2025-08-10 11:52:14,840 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-08-10 11:52:14,843 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for testmahmoud
2025-08-10 11:52:14,845 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-08-10 11:52:14,850 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-08-10 11:52:14,852 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-08-10 11:52:14,856 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-08-10 11:52:14,859 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-08-10 11:52:14,861 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for testmahmoud
2025-08-10 11:52:14,869 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-08-10 11:52:14,877 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-08-10 11:52:14,882 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-08-10 11:52:14,884 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-08-10 11:52:14,887 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for testmahmoud
2025-08-10 11:52:14,889 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-08-10 11:52:14,901 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-08-10 11:52:14,907 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-08-10 11:52:14,909 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for testmahmoud
2025-08-10 11:52:14,913 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-08-10 11:52:14,915 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-08-10 11:52:14,924 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-08-10 11:52:14,927 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for testmahmoud
2025-08-10 11:52:14,940 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for testmahmoud
2025-08-10 11:52:14,943 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-08-10 11:52:14,948 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for testmahmoud
2025-08-13 13:51:28,701 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for testmahmoud
2025-08-13 13:51:28,708 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for testmahmoud
2025-08-13 13:51:28,710 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for testmahmoud
2025-08-13 13:51:28,716 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for testmahmoud
2025-08-13 13:51:28,723 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for testmahmoud
2025-08-13 13:51:28,726 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for testmahmoud
2025-08-13 13:51:28,728 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for testmahmoud
2025-08-13 13:51:28,733 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for testmahmoud
2025-08-13 13:51:28,735 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for testmahmoud
2025-08-13 13:51:28,742 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for testmahmoud
2025-08-13 13:51:28,745 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for testmahmoud
2025-08-13 13:51:28,746 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for testmahmoud
2025-08-13 13:51:28,749 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for testmahmoud
2025-08-13 13:51:28,761 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for testmahmoud
2025-08-13 13:51:28,763 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for testmahmoud
2025-08-13 13:51:28,768 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for testmahmoud
2025-08-13 13:51:28,773 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for testmahmoud
2025-08-13 13:51:28,779 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for testmahmoud
2025-08-13 13:51:28,780 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for testmahmoud
2025-08-13 13:51:28,782 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for testmahmoud
2025-08-13 13:51:28,785 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for testmahmoud
2025-08-13 13:51:28,787 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for testmahmoud
2025-08-13 13:51:28,795 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for testmahmoud
2025-08-13 13:51:28,798 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for testmahmoud
2025-08-13 13:51:28,800 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for testmahmoud
2025-08-13 13:51:28,804 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for testmahmoud
2025-08-13 13:51:28,808 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for testmahmoud
2025-08-13 13:51:29,349 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for featuretracker
2025-08-13 13:51:29,355 ERROR scheduler Skipped queueing iam.utils.otp.delete_otps because it was found in queue for featuretracker
2025-08-13 13:51:29,360 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for featuretracker
2025-08-13 13:51:29,362 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for featuretracker
2025-08-13 13:51:29,371 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for featuretracker
2025-08-13 13:51:29,374 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for featuretracker
2025-08-13 13:51:29,397 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for featuretracker
2025-08-13 13:51:29,399 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for featuretracker
2025-08-13 13:51:29,403 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for featuretracker
2025-08-13 13:51:29,405 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for featuretracker
