2025-02-05 22:10:02,631 INFO ipython === bench console session ===
2025-02-05 22:10:02,632 INFO ipython frappe.db.sql("""
        SELECT disability, family_member_disability 
        FROM `tabBeneficiary` 
        WHERE national_id = %s
    """, (national_id,), as_dict=1)
2025-02-05 22:10:02,632 INFO ipython frappe.db.sql("""
        SELECT disability, family_member_disability 
        FROM `tabBeneficiary` 
        WHERE national_id = %s
    """, (30010150101492,), as_dict=1)
2025-02-05 22:10:02,633 INFO ipython national_id = 30010150101492
frappe.db.sql("""
        SELECT disability, family_member_disability 
        FROM `tabBeneficiary` 
        WHERE national_id = %s
    """, (national_id,), as_dict=1)
2025-02-05 22:10:02,633 INFO ipython national_id = '30010150101492'
frappe.db.sql("""
        SELECT disability, family_member_disability 
        FROM `tabBeneficiary` 
        WHERE national_id = %s
    """, (national_id,), as_dict=1)
2025-02-05 22:10:02,633 INFO ipython === session end ===
2025-02-07 18:24:23,260 INFO ipython === bench console session ===
2025-02-07 18:24:23,261 INFO ipython         result = frappe.db.sql("""
            SELECT disability, family_member_disability 
            FROM `tabBeneficiary` 
            WHERE name = %s
        """, ('30010150101492'), as_dict=1)
2025-02-07 18:24:23,263 INFO ipython print(result)
2025-02-07 18:24:23,264 INFO ipython         result = frappe.db.sql("""
            SELECT disability, family_member_disability 
            FROM `tabBeneficiary` 
            WHERE name = %s
        """, ('30010150101492'), as_dict=1)
2025-02-07 18:24:23,265 INFO ipython print(result)
2025-02-07 18:24:23,266 INFO ipython === session end ===
2025-02-09 11:22:56,044 INFO ipython === bench console session ===
2025-02-09 11:22:56,046 INFO ipython x =frappe.get_doc("Beneficiary", "حسين كامل")
2025-02-09 11:22:56,046 INFO ipython x =frappe.get_doc("Beneficiary", "30010150101222")
2025-02-09 11:22:56,046 INFO ipython print(x)
2025-02-09 11:22:56,046 INFO ipython print(x.full_name)
2025-02-09 11:22:56,046 INFO ipython === session end ===
2025-02-25 11:12:12,318 INFO ipython === bench console session ===
2025-02-25 11:12:12,320 INFO ipython print("soso")
2025-02-25 11:12:12,320 INFO ipython frappe.db.truncate("Has Role")
2025-02-25 11:12:12,321 INFO ipython frappe.db.commit()
2025-02-25 11:12:12,321 INFO ipython === session end ===
2025-04-07 15:57:11,668 INFO ipython === bench console session ===
2025-04-07 15:57:11,668 INFO ipython from ngo.ngo_loan.doctype.loan_request.loan_request import refresh_feasibility_studies_status
2025-04-07 15:57:11,669 INFO ipython from ngo.ngo_loan.doctype.loan_request.loan_request import unshare_docs
2025-04-07 15:57:11,669 INFO ipython from ngo.ngo_loan.doctype.loan_request.loan_request import *
2025-04-07 15:57:11,669 INFO ipython refresh_feasibility_studies_status()
2025-04-07 15:57:11,669 INFO ipython from ngo.ngo_loan.doctype.loan_request.loan_request import refresh_feasibility_studies_status
2025-04-07 15:57:11,669 INFO ipython from ngo.ngo_loan.doctype.loan_request.loan_request import refresh_feasibility_studies_status
2025-04-07 15:57:11,669 INFO ipython === session end ===
2025-04-07 16:25:10,223 INFO ipython === bench console session ===
2025-04-07 16:25:10,223 INFO ipython from ngo.ngo_loan.doctype.loan_request.loan_request import refresh_feasibility_studies_status
2025-04-07 16:25:10,223 INFO ipython refresh_feasibility_studies_status()
2025-04-07 16:25:10,223 INFO ipython refresh_feasibility_studies_status("FS-2025-03-1")
2025-04-07 16:25:10,223 INFO ipython refresh_feasibility_studies_status("FS-2025-03-1")
2025-04-07 16:25:10,224 INFO ipython frappe.get_doc("Loan Request")
2025-04-07 16:25:10,224 INFO ipython refresh_feasibility_studies_status("FS-2025-03-1")
2025-04-07 16:25:10,224 INFO ipython === session end ===
2025-05-04 17:29:58,988 INFO ipython === bench console session ===
2025-05-04 17:29:58,988 INFO ipython frappe.utils.nowdate()
2025-05-04 17:29:58,988 INFO ipython frappe.datetime.get_day_diff("2025-03-11","2025-03-11")
2025-05-04 17:29:58,989 INFO ipython from frappe.utils import date_diff
2025-05-04 17:29:58,989 INFO ipython date_diff("2025-03-11","2025-03-11")
2025-05-04 17:29:58,989 INFO ipython date_diff("2025-03-11","2025-03-12")
2025-05-04 17:29:58,989 INFO ipython date_diff("2025-03-13","2025-03-12")
2025-05-04 17:29:58,989 INFO ipython === session end ===
2025-05-11 14:57:57,785 INFO ipython === bench console session ===
2025-05-11 14:57:57,787 INFO ipython === session end ===
2025-05-22 14:40:14,717 INFO ipython === bench console session ===
2025-05-22 14:40:14,719 INFO ipython frappe.db.exists("DocType", "Customer NGO Attachments")
2025-05-22 14:40:14,719 INFO ipython frappe.db.exists("DocType", "Customer NGO Attachmentsd")
2025-05-22 14:40:14,719 INFO ipython print(frappe.db.exists("DocType", "Customer NGO Attachments"))
2025-05-22 14:40:14,719 INFO ipython print(frappe.db.exists("DocType", "Customer NGO Attachmentsd"))
2025-05-22 14:40:14,719 INFO ipython === session end ===
2025-05-22 15:41:34,882 INFO ipython === bench console session ===
2025-05-22 15:41:34,883 INFO ipython frappe.db.table_exists("Customer NGO Attachments")
2025-05-22 15:41:34,883 INFO ipython from frappe.utils.install import sync_tables
2025-05-22 15:41:34,884 INFO ipython === session end ===
2025-05-22 16:05:30,925 INFO ipython === bench console session ===
2025-05-22 16:05:30,926 INFO ipython frappe.db.truncate("Has Role")
2025-05-22 16:05:30,926 INFO ipython frappe.db.commit()
2025-05-22 16:05:30,926 INFO ipython === session end ===
2025-05-25 16:31:33,619 INFO ipython === bench console session ===
2025-05-25 16:31:33,621 INFO ipython frappe.db.get_system_setting("deny_multiple_sessions")
2025-05-25 16:31:33,621 INFO ipython === session end ===
2025-06-10 21:18:08,163 INFO ipython === bench console session ===
2025-06-10 21:18:08,164 INFO ipython import frappe
2025-06-10 21:18:08,164 INFO ipython iam_settings = frappe.get_doc("IAM Settings")
2025-06-10 21:18:08,165 INFO ipython print("Enable roles login:", iam_settings.enable_roles_login)
2025-06-10 21:18:08,165 INFO ipython if iam_settings.enable_roles_login:
        print("Login roles:", iam_settings.get_login_roles())
        print("Administrator roles:", frappe.get_roles("Administrator"))
        print("Administrator roles:", frappe.get_roles("Administrator"))
        print("Administrator roles:", frappe.get_roles("Administrator"))
        print("Administrator roles:", frappe.get_roles("Administrator"))
        
2025-06-10 21:18:08,165 INFO ipython frappe.get_roles("Administrator")
2025-06-10 21:18:08,165 INFO ipython admin_user = frappe.get_doc("User", "Administrator")
2025-06-10 21:18:08,165 INFO ipython print("User type:", admin_user.user_type)
2025-06-10 21:18:08,165 INFO ipython print("Enabled:", admin_user.enabled)
2025-06-10 21:18:08,165 INFO ipython print("Block website login setting:", iam_settings.block_website_login)
2025-06-10 21:18:08,165 INFO ipython # Let's test the login process step by step
2025-06-10 21:18:08,165 INFO ipython import frappe
2025-06-10 21:18:08,166 INFO ipython from frappe.auth import LoginManager
2025-06-10 21:18:08,166 INFO ipython # Test login manager creation
2025-06-10 21:18:08,166 INFO ipython login_manager = LoginManager()
2025-06-10 21:18:08,166 INFO ipython print("Login manager created successfully")
2025-06-10 21:18:08,166 INFO ipython # Test setting credentials
2025-06-10 21:18:08,166 INFO ipython login_manager.user = "Administrator"
2025-06-10 21:18:08,166 INFO ipython login_manager.pwd = "admin"  # Replace with actual password
2025-06-10 21:18:08,166 INFO ipython print("Credentials set")
2025-06-10 21:18:08,166 INFO ipython # Test authentication
2025-06-10 21:18:08,166 INFO ipython try:
        login_manager.authenticate()
            print("Authentication successful")
2025-06-10 21:18:08,166 INFO ipython except Exception as e:
        print(f"Authentication failed: {e}")
2025-06-10 21:18:08,166 INFO ipython     print(f"Error type: {type(e)}")
2025-06-10 21:18:08,167 INFO ipython # Let's check if there are any authentication hooks that might be blocking Administrator
2025-06-10 21:18:08,167 INFO ipython import iam
2025-06-10 21:18:08,167 INFO ipython from iam.utils.auth.hooks import on_login
2025-06-10 21:18:08,167 INFO ipython # Check if IAM is overriding authentication
2025-06-10 21:18:08,167 INFO ipython print("IAM validate_auth function:", iam.validate_auth)
2025-06-10 21:18:08,167 INFO ipython print("Original frappe validate_auth:", frappe.auth.validate_auth)
2025-06-10 21:18:08,167 INFO ipython # Check if there are any specific restrictions on Administrator user
2025-06-10 21:18:08,167 INFO ipython admin_doc = frappe.get_doc("User", "Administrator")
2025-06-10 21:18:08,167 INFO ipython print("Administrator enabled:", admin_doc.enabled)
2025-06-10 21:18:08,167 INFO ipython print("Administrator user_type:", admin_doc.user_type)
2025-06-10 21:18:08,167 INFO ipython print("Administrator roles:", frappe.get_roles("Administrator"))
2025-06-10 21:18:08,168 INFO ipython # Let's check the IAM authentication function
2025-06-10 21:18:08,168 INFO ipython import inspect
2025-06-10 21:18:08,168 INFO ipython print("IAM authenticate function source:")
2025-06-10 21:18:08,168 INFO ipython print(inspect.getsource(iam.authenticate))
2025-06-10 21:18:08,168 INFO ipython # Let's also check if there are any specific restrictions
2025-06-10 21:18:08,168 INFO ipython print("\nChecking IAM flags:")
2025-06-10 21:18:08,168 INFO ipython print("JWT enabled:", iam.flags.is_jwt_enabled if hasattr(iam.flags, 'is_jwt_enabled') else "Not set")
2025-06-10 21:18:08,168 INFO ipython print("JWT required:", iam.flags.is_jwt_required if hasattr(iam.flags, 'is_jwt_required') else "Not set")
2025-06-10 21:18:08,168 INFO ipython # Let's check what iam.validate_auth does
2025-06-10 21:18:08,168 INFO ipython print("iam.validate_auth source:")
2025-06-10 21:18:08,168 INFO ipython print(inspect.getsource(iam.validate_auth))
2025-06-10 21:18:08,168 INFO ipython # Let's also check if iam.request.is_api is set correctly
2025-06-10 21:18:08,168 INFO ipython print("\niam.request.is_api:", iam.request.is_api if hasattr(iam.request, 'is_api') else "Not set")
2025-06-10 21:18:08,169 INFO ipython # Let's check the original frappe validate_auth
2025-06-10 21:18:08,169 INFO ipython print("\nOriginal frappe validate_auth:")
2025-06-10 21:18:08,169 INFO ipython original_validate_auth = frappe.auth.validate_auth
2025-06-10 21:18:08,169 INFO ipython print(inspect.getsource(original_validate_auth))
2025-06-10 21:18:08,169 INFO ipython frappe.get_doc("Loan Request")
2025-06-10 21:18:08,169 INFO ipython frappe.get_doc("tabLoan Request")
2025-06-10 21:18:08,169 INFO ipython frappe.get_doc("Loan Request","LR-53")
2025-06-10 21:18:08,169 INFO ipython frappe.db.get_doc("Loan Request","LR-53")
2025-06-10 21:18:08,169 INFO ipython frappe.db.get_value("Loan Request","LR-53")
2025-06-10 21:18:08,169 INFO ipython frappe.db.get_value("Loan Request")
2025-06-10 21:18:08,169 INFO ipython print(frappe.db.get_value("Loan Request"))
2025-06-10 21:18:08,169 INFO ipython print(frappe.db.get_value("Loan Request",{"name":"LR-53"}))
2025-06-10 21:18:08,169 INFO ipython print(frappe.get_doc("iScore","ISC-2025-06-10-3"))
2025-06-10 21:18:08,169 INFO ipython print(frappe.get_doc("iScore","ISC-2025-06-10-3"))
2025-06-10 21:18:08,170 INFO ipython print(frappe.get_doc("iScore","ISC-2025-06-10-3"))
2025-06-10 21:18:08,170 INFO ipython === session end ===
2025-06-10 22:02:52,247 INFO ipython === bench console session ===
2025-06-10 22:02:52,247 INFO ipython frappe.get_doc("LR Board Member Details","bb6c393hi8")
2025-06-10 22:02:52,248 INFO ipython doc = frappe.get_doc("LR Board Member Details","bb6c393hi8")
2025-06-10 22:02:52,248 INFO ipython doc.name
2025-06-10 22:02:52,248 INFO ipython doc = frappe.get_doc("Loan Request","LR-55")
2025-06-10 22:02:52,248 INFO ipython doc.board_members
2025-06-10 22:02:52,248 INFO ipython doc.board_members.doctype
2025-06-10 22:02:52,248 INFO ipython doc.board_members[0]
2025-06-10 22:02:52,248 INFO ipython doc.board_members[0].doctype
2025-06-10 22:02:52,248 INFO ipython doc.board_members[0].name
2025-06-10 22:02:52,248 INFO ipython help(doc.board_members[0])
2025-06-10 22:02:52,248 INFO ipython doc = frappe.get_doc("Loan Request","LR-56")
2025-06-10 22:02:52,248 INFO ipython doc = frappe.get_doc("Loan Request","LR-56")
2025-06-10 22:02:52,248 INFO ipython === session end ===
2025-06-10 22:03:38,121 INFO ipython === bench console session ===
2025-06-10 22:03:38,122 INFO ipython doc = frappe.get_doc("Loan Request","LR-56")
2025-06-10 22:03:38,122 INFO ipython doc.board_members[0].name
2025-06-10 22:03:38,122 INFO ipython doc.board_members[0].parent
2025-06-10 22:03:38,122 INFO ipython === session end ===
2025-06-18 14:13:02,992 INFO ipython === bench console session ===
2025-06-18 14:13:02,993 INFO ipython import frappe
2025-06-18 14:13:02,993 INFO ipython frappe.db.get_single_value("System Settings", "reset_password_link_expiry_duration")
2025-06-18 14:13:02,993 INFO ipython # Check for users with reset password keys
2025-06-18 14:13:02,993 INFO ipython frappe.db.sql("SELECT name, reset_password_key, last_reset_password_key_generated_on FROM tabUser WHERE reset_password_key IS NOT NULL AND reset_password_key != ''", as_dict=True)
2025-06-18 14:13:02,993 INFO ipython from frappe.utils import now_datetime
2025-06-18 14:13:02,993 INFO ipython from datetime import timedelta
2025-06-18 14:13:02,993 INFO ipython current_time = now_datetime()
2025-06-18 14:13:02,993 INFO ipython expiry_duration = 172800  # seconds (48 hours)
2025-06-18 14:13:02,994 INFO ipython print(f"Current time: {current_time}")
2025-06-18 14:13:02,994 INFO ipython print(f"Expiry duration: {expiry_duration} seconds ({expiry_duration/3600} hours)")
2025-06-18 14:13:02,994 INFO ipython # Check which keys are expired
2025-06-18 14:13:02,994 INFO ipython for user in frappe.db.sql("SELECT name, reset_password_key, last_reset_password_key_generated_on FROM tabUser WHERE reset_password_key IS NOT NULL AND reset_password_key != ''", as_dict=True):
        key_time = user['last_reset_password_key_generated_on']
            expired = current_time > key_time + timedelta(seconds=expiry_duration)
2025-06-18 14:13:02,994 INFO ipython     print(f"{user['name']}: Generated {key_time}, Expired: {expired}")
2025-06-18 14:13:02,994 INFO ipython for user in frappe.db.sql("SELECT name, reset_password_key, last_reset_password_key_generated_on FROM tabUser WHERE reset_password_key IS NOT NULL AND reset_password_key != ''", as_dict=True):
        key_time = user['last_reset_password_key_generated_on']
            expired = current_time > key_time + timedelta(seconds=expiry_duration)
2025-06-18 14:13:02,994 INFO ipython     print(f"{user['name']}: Generated {key_time}, Expired: {expired}")
2025-06-18 14:13:02,994 INFO ipython === session end ===
2025-06-22 14:23:14,329 INFO ipython === bench console session ===
2025-06-22 14:23:14,331 INFO ipython === session end ===
2025-06-22 14:38:16,069 INFO ipython === bench console session ===
2025-06-22 14:38:16,070 INFO ipython frappe.db.truncate("Has Role")
2025-06-22 14:38:16,070 INFO ipython frappe.db.commit()
2025-06-22 14:38:16,070 INFO ipython === session end ===
2025-07-15 12:37:12,387 INFO ipython === bench console session ===
2025-07-15 12:37:12,387 INFO ipython frappe.db.get_value("Warehouses", {"item_code": item_code}, "warehouse_name")
2025-07-15 12:37:12,387 INFO ipython frappe.db.get_value("Warehouses", {"item_code": SKU0030}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"item_code": 'SKU0030'}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"item_code": SKU0030}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"item_code": 'SKU0030'}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": order.company}, "warehouse_name"
)
2025-07-15 12:37:12,388 INFO ipython frappe.defaults.get_user_default("Company")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'})
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'},'All Warehouses - AFD')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'},'name')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'},'id')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouse", {"company": 'Amer Fragrance (Demo)'},'id')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouse", {"company": 'Amer Fragrance (Demo)'},'name')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouse", {"company": 'Amer Fragrance (Demo)'},'name')
2025-07-15 12:37:12,388 INFO ipython ثءهف
2025-07-15 12:37:12,388 INFO ipython === session end ===
2025-07-20 15:22:54,647 INFO ipython === bench console session ===
2025-07-20 15:22:54,650 INFO ipython fields = ["bod_meetings_record", "ga_meetings_record", "ms_record", "visits_record", "bwl_record", "donations_record", "cash_receipts", "bank_deposits", "exchange_notes", "cheque_permits", "bookkeeping", "daily_journals", "ti_payments", "beneficiaries_portfolios", "daily_journals_records", "customer_record_book", "expenses_analytical_journal", "ie_journal", "treasury_journal", "bank_book", "assets_register", "budget_account", "re_account", "rp_account", "cash_flow_account", "draft_budget_account", "fa_report", "comment", "previous_works_status", "committee_decision", "cash_flow_form_status"]
for field in fields:
    print(frappe.meta.get_docfield("Financial Investigation", field).label)
    
2025-07-20 15:22:54,650 INFO ipython fields = ["bod_meetings_record", "ga_meetings_record", "ms_record", "visits_record", "bwl_record", "donations_record", "cash_receipts", "bank_deposits", "exchange_notes", "cheque_permits", "bookkeeping", "daily_journals", "ti_payments", "beneficiaries_portfolios", "daily_journals_records", "customer_record_book", "expenses_analytical_journal", "ie_journal", "treasury_journal", "bank_book", "assets_register", "budget_account", "re_account", "rp_account", "cash_flow_account", "draft_budget_account", "fa_report", "comment", "previous_works_status", "committee_decision", "cash_flow_form_status"]
for field in fields:
    meta = frappe.get_meta("Financial Investigation")
    label = meta.get_label(field)
    print(label)
    
2025-07-20 15:22:54,650 INFO ipython 
fields = ["daily_journals_records", "customer_record_book", "expenses_analytical_journal", "ie_journal", "treasury_journal", "bank_book", "assets_register"]
for field in fields:
    meta = frappe.get_meta("Financial Investigation")
    label = meta.get_label(field)
    print(label)
    
2025-07-20 15:22:54,650 INFO ipython === session end ===
2025-08-01 10:34:47,959 INFO ipython === bench console session ===
2025-08-01 10:34:47,960 INFO ipython frappe.get_All("Feature Request")
2025-08-01 10:34:47,960 INFO ipython frappe.getAll("Feature Request")
2025-08-01 10:34:47,961 INFO ipython frappe.get_all("Feature Request")
2025-08-01 10:34:47,961 INFO ipython meta = frappe.get_meta("Feature Request")
2025-08-01 10:34:47,961 INFO ipython meta
2025-08-01 10:34:47,961 INFO ipython print(meta)
2025-08-01 10:34:47,961 INFO ipython fieldnames = [df.fieldname for df in meta.fields]
2025-08-01 10:34:47,961 INFO ipython print(fieldnames)
2025-08-01 10:34:47,961 INFO ipython fieldnames = [df for df in meta.fields]
2025-08-01 10:34:47,961 INFO ipython print(fieldnames)
2025-08-01 10:34:47,961 INFO ipython meta = frappe.get_meta("Feature Request")

# Get all fieldnames
DEFAULT_FIELDS = [df.fieldname for df in meta.fields if df.fieldname]

# Get options for 'priority' field
priority_field = next((df for df in meta.fields if df.fieldname == "priority"), None)
VALID_PRIORITIES = priority_field.options.splitlines() if priority_field and priority_field.options else []

# Get options for 'status' field
status_field = next((df for df in meta.fields if df.fieldname == "status"), None)
VALID_STATUSES = status_field.options.splitlines() if status_field and status_field.options else []

# Print them for verification
print("DEFAULT_FIELDS:", DEFAULT_FIELDS)
print("VALID_PRIORITIES:", VALID_PRIORITIES)
print("VALID_STATUSES:", VALID_STATUSES)
2025-08-01 10:34:47,961 INFO ipython meta = frappe.get_meta("Feature Request")

# Get all fieldnames
DEFAULT_FIELDS = [df.fieldname for df in meta.fields if df.fieldname]
DEFAULT_FIELDS = [
    df.fieldname for df in meta.fields
    if df.fieldtype not in ["Section Break", "Column Break", "Tab Break"]
    and df.hidden != 1
    and df.fieldname  # تأكد أنه مش فاضي
]

# Get options for 'priority' field
priority_field = next((df for df in meta.fields if df.fieldname == "priority"), None)
VALID_PRIORITIES = priority_field.options.splitlines() if priority_field and priority_field.options else []

# Get options for 'status' field
status_field = next((df for df in meta.fields if df.fieldname == "status"), None)
VALID_STATUSES = status_field.options.splitlines() if status_field and status_field.options else []

# Print them for verification
print("DEFAULT_FIELDS:", DEFAULT_FIELDS)
print("VALID_PRIORITIES:", VALID_PRIORITIES)
print("VALID_STATUSES:", VALID_STATUSES)
2025-08-01 10:34:47,961 INFO ipython meta = frappe.get_meta("Feature Request")

DEFAULT_FIELDS = ["name"] + [
    df.fieldname for df in meta.fields
    if df.fieldtype not in ["Section Break", "Column Break", "Tab Break"]
    and not df.hidden
    and df.fieldname
    and df.fieldname not in ["amended_from"]
]

priority_field = meta.get_field("priority")
VALID_PRIORITIES = priority_field.options.splitlines() if priority_field and priority_field.options else []

status_field = meta.get_field("status")
VALID_STATUSES = status_field.options.splitlines() if status_field and status_field.options else []
2025-08-01 10:34:47,961 INFO ipython     ...: print("DEFAULT_FIELDS:", DEFAULT_FIELDS)
    ...: print("VALID_PRIORITIES:", VALID_PRIORITIES)
    ...: print("VALID_STATUSES:", VALID_STATUSES)
2025-08-01 10:34:47,961 INFO ipython === session end ===
2025-08-11 12:58:54,116 INFO ipython === bench console session ===
2025-08-11 12:58:54,119 INFO ipython user = frappe.session.user
company = frappe.db.get_value("User", user, "default_company")
2025-08-11 12:58:54,119 INFO ipython user = frappe.session.user
company = frappe.db.get_value("User", user, "company")
2025-08-11 12:58:54,119 INFO ipython frappe.defaults.get_user_default("Company")
2025-08-11 12:58:54,119 INFO ipython === session end ===
2025-08-13 13:52:02,450 INFO ipython === bench console session ===
2025-08-13 13:52:02,450 INFO ipython import fr
2025-08-13 13:52:02,450 INFO ipython import frappe
2025-08-13 13:52:02,450 INFO ipython current_user = frappe.session.user
2025-08-13 13:52:02,450 INFO ipython print(current_user)
2025-08-13 13:52:02,450 INFO ipython def is_valid_user_role_profile(role_profile):
    # Validates if the user has the specified role_profile
    current_user = frappe.session.user
    user_roles = frappe.get_roles(current_user)
    if role_profile not in user_roles:
        return False
    return True
    
2025-08-13 13:52:02,450 INFO ipython is_valid_user_role_profile("Super")
2025-08-13 13:52:02,450 INFO ipython is_valid_user_role_profile("Authinticated")
2025-08-13 13:52:02,450 INFO ipython is_valid_user_role_profile("Desk")
2025-08-13 13:52:02,450 INFO ipython is_valid_user_role_profile("Desk User")
2025-08-13 13:52:02,450 INFO ipython === session end ===
2025-08-13 14:00:00,628 INFO ipython === bench console session ===
2025-08-13 14:00:00,628 INFO ipython current_user = frappe.session.user
2025-08-13 14:00:00,628 INFO ipython print(current_user)
2025-08-13 14:00:00,628 INFO ipython def is_valid_user_role_profile(role_profile):
    # Validates if the user has the specified role_profile
    current_user = frappe.session.user
    user_roles = frappe.get_roles(current_user)
    if role_profile not in user_roles:
        return False
    return True
    
2025-08-13 14:00:00,628 INFO ipython is_valid_user_role_profile("iScore Editor")
2025-08-13 14:00:00,628 INFO ipython is_valid_user_role_profile("iScore Edito")
2025-08-13 14:00:00,628 INFO ipython is_valid_user_role_profile("iScore Editor")
2025-08-13 14:00:00,628 INFO ipython === session end ===
2025-08-13 14:16:29,944 INFO ipython === bench console session ===
2025-08-13 14:16:29,946 INFO ipython def has_role_profile(role_profile):
    """Check if the current user has the given Role Profile."""
    current_user = frappe.session.user
    role_profiles = frappe.get_all(
        "Has Role Profile",
        filters={
            "parent": current_user,
            "parenttype": "User",
            "role_profile": role_profile
        },
        pluck="role_profile"
    )
    return bool(role_profiles)
    
2025-08-13 14:16:29,946 INFO ipython has_role_profile("Desk")
2025-08-13 14:16:29,946 INFO ipython def has_role_profile(role_profile):
    """Check if the current user has the given Role Profile."""
    current_user = frappe.session.user
    role_profiles = frappe.get_all(
        "Role Profile",
        filters={
            "parent": current_user,
            "parenttype": "User",
            "role_profile": role_profile
        },
        pluck="role_profile"
    )
    return bool(role_profiles)
    
2025-08-13 14:16:29,946 INFO ipython has_role_profile("Desk")
2025-08-13 14:16:29,946 INFO ipython === session end ===
2025-08-13 14:18:47,184 INFO ipython === bench console session ===
2025-08-13 14:18:47,185 INFO ipython frappe.user.has_role(“Role”)
2025-08-13 14:18:47,185 INFO ipython frappe.user.has_role("Desk")
2025-08-13 14:18:47,185 INFO ipython === session end ===
