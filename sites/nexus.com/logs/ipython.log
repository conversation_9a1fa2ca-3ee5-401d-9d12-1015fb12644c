2025-07-15 12:37:12,387 INFO ipython === bench console session ===
2025-07-15 12:37:12,387 INFO ipython frappe.db.get_value("Warehouses", {"item_code": item_code}, "warehouse_name")
2025-07-15 12:37:12,387 INFO ipython frappe.db.get_value("Warehouses", {"item_code": SKU0030}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"item_code": 'SKU0030'}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"item_code": SKU0030}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"item_code": 'SKU0030'}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": order.company}, "warehouse_name"
)
2025-07-15 12:37:12,388 INFO ipython frappe.defaults.get_user_default("Company")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'}, "warehouse_name")
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'})
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'},'All Warehouses - AFD')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'},'name')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouses", {"company": 'Amer Fragrance (Demo)'},'id')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouse", {"company": 'Amer Fragrance (Demo)'},'id')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouse", {"company": 'Amer Fragrance (Demo)'},'name')
2025-07-15 12:37:12,388 INFO ipython frappe.db.get_value("Warehouse", {"company": 'Amer Fragrance (Demo)'},'name')
2025-07-15 12:37:12,388 INFO ipython ثءهف
2025-07-15 12:37:12,388 INFO ipython === session end ===
2025-08-11 12:58:54,116 INFO ipython === bench console session ===
2025-08-11 12:58:54,119 INFO ipython user = frappe.session.user
company = frappe.db.get_value("User", user, "default_company")
2025-08-11 12:58:54,119 INFO ipython user = frappe.session.user
company = frappe.db.get_value("User", user, "company")
2025-08-11 12:58:54,119 INFO ipython frappe.defaults.get_user_default("Company")
2025-08-11 12:58:54,119 INFO ipython === session end ===
2025-08-13 13:52:02,450 INFO ipython === bench console session ===
2025-08-13 13:52:02,450 INFO ipython import fr
2025-08-13 13:52:02,450 INFO ipython import frappe
2025-08-13 13:52:02,450 INFO ipython current_user = frappe.session.user
2025-08-13 13:52:02,450 INFO ipython print(current_user)
2025-08-13 13:52:02,450 INFO ipython def is_valid_user_role_profile(role_profile):
    # Validates if the user has the specified role_profile
    current_user = frappe.session.user
    user_roles = frappe.get_roles(current_user)
    if role_profile not in user_roles:
        return False
    return True
    
2025-08-13 13:52:02,450 INFO ipython is_valid_user_role_profile("Super")
2025-08-13 13:52:02,450 INFO ipython is_valid_user_role_profile("Authinticated")
2025-08-13 13:52:02,450 INFO ipython is_valid_user_role_profile("Desk")
2025-08-13 13:52:02,450 INFO ipython is_valid_user_role_profile("Desk User")
2025-08-13 13:52:02,450 INFO ipython === session end ===
