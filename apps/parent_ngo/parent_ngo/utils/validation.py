import frappe
from datetime import datetime
from frappe.utils import get_date_str

def is_valid_dob(dob, nid):
    # Validates the provided DOB matches the National ID
    century = str(19 + int(nid[0]) - 2)
    nid_dob = century + nid[1:7]
    return (get_date_str(dob) == get_date_str(datetime.strptime(nid_dob, "%Y%m%d")))

def is_valid_mobile_number(mobile_no):
    # Validates if the provided mobile number length is 11
    # and if it is a valid Egyptian mobile number
    valid_mobile_no_prefixes = ["010", "011", "012", "015"]
    return (len(mobile_no) == 11 and mobile_no.isdigit() and mobile_no[:3] in valid_mobile_no_prefixes)

def has_role(role):
    """Check if the current user has the specified role.
    Args:
        role (str): The role to check for.
    Returns:
        bool: True if the user has the role, False otherwise.
    """
    current_user = frappe.session.user
    user_roles = frappe.get_roles(current_user)
    return role in user_roles
