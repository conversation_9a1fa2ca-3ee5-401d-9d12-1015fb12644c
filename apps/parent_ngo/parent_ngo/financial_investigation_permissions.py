import frappe


def get_permission_query_conditions(user):
    if not user or user == "Administrator":
        return ""

    # Get allowed Loan Programs, and Customer <PERSON><PERSON> for this user
    user_permissions = frappe.get_all(
        "User Permission",
        filters={"user": user, "allow": ["in", ["Customer Ngo", "Loan Program"]]},
        fields=["allow", "for_value"]
    )
    # prepare for sql values
    allowed_orgs = [f"'{p['for_value']}'" for p in user_permissions if p["allow"] == "Customer NGO"]
    allowed_programs = [f"'{p['for_value']}'" for p in user_permissions if p["allow"] == "Loan Program"]
    if not allowed_programs or not allowed_orgs:
        return "1=0"  # deny all if user has no allowed programs or organizations

    allowed_orgs_str = ", ".join(allowed_orgs)
    allowed_programs_str = ", ".join(allowed_programs)
    
    # Return condition that ensures investigation is linked via loan request to allowed loan program and Customer Ngo(organization)
    return f"""
        EXISTS (
            SELECT 1 FROM `tabLoan Request` lr
            WHERE lr.name = `tabFinancial Investigation`.loan_request
            AND lr.loan_program IN ({allowed_programs_str})
            AND lr.ngo IN ({allowed_orgs_str})
        )
    """

def has_permission(doc, user):
    if user == "Administrator":
        return True

    # Get allowed Loan Programs, and Customer Ngo for this user
    user_permissions = frappe.get_all(
        "User Permission",
        filters={"user": user, "allow": ["in", ["Customer Ngo", "Loan Program"]]},
        fields=["allow", "for_value"]
    )

    allowed_orgs = [p['for_value'] for p in user_permissions if p["allow"] == "Customer NGO"]
    allowed_programs = [p['for_value'] for p in user_permissions if p["allow"] == "Loan Program"]

    loan_request = frappe.get_doc("Loan Request", doc.loan_request)
    return loan_request.ngo in allowed_orgs and loan_request.loan_program in allowed_programs
