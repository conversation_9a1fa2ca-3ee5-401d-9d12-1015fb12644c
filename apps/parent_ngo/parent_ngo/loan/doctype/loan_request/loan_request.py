# Copyright (c) 2024, <PERSON>W<PERSON> and contributors
# For license information, please see license.txt

import frappe, iam, parent_ngo
from frappe import _, share
from frappe.utils import nowdate
from parent_ngo.utils.beneficiary import insert_beneficiary
from parent_ngo.utils.validation import has_role
from frappe.model.document import Document


class LoanRequest(Document):
	def before_validate(self):
		self.set_docs()
		self.set_creation_date()
		self.set_terms()
		self.calculate_beneficiaries_total_loan_amount()

	def validate(self):
		self.validate_ngo_members()
		self.validate_beneficiaries()
		self.validate_loan_program_criteria()
		self.validate_attachments()
		self.validate_terms()
		self.validate_review()
		self.validate_contract_details()

	def before_save(self):
		self.set_ngo_members_attachments()
		self.set_beneficiary_documents()
		self.delete_beneficiary_documents()
		self.create_loan()

	def on_update(self):
		self.share_with_self()
		self.submit_iscore_requests()

	def set_docs(self):
		# Sets NGO documents for easy access
		self.ngo_doc = frappe.get_doc("Customer NGO", self.ngo)
		self.loan_program_doc = frappe.get_doc("Loan Program", self.loan_program) if self.loan_program else {}

	def set_creation_date(self):
		# Sets creation date to today if not set
		if not self.creation_date:
			self.creation_date = nowdate()

	def set_terms(self):
		# Sets Terms & Conditions if not set
		if not self.terms:
			self.terms = frappe.db.get_value("Policy", "Pledge to Pay", "content")

	def calculate_beneficiaries_total_loan_amount(self):
		# Calculates the total loan amount of all beneficiaries
		total_loan_amount = 0
		for beneficiary in self.beneficiaries:
			total_loan_amount += beneficiary.loan_amount

	def validate_ngo_members(self):
		# Validates that all NGO Members IDs are uploaded
		if self.status in ["Draft", "Pending CN Review"]:
			return
		self.validate_ngo_members_ids()
		self.validate_ngo_board_members_ids()

	def validate_ngo_members_ids(self):
		# Validates that Contact Person & Treasurer IDs are uploaded
		attachment_fields = ["contact_nid_front", "contact_nid_back", "treasurer_nid_front", "treasurer_nid_back", "ceo_nid_front", "ceo_nid_back"]
		for field in attachment_fields:
			if not self.get(field):
				exception = iam.MandatoryFieldError(targets=field)
				iam.throw(_(parent_ngo.ERRORS.get(f"missing_{field}")), exception=exception)

	def validate_ngo_board_members_ids(self):
		# Validates that Board Members IDs are uploaded
		for member in self.board_members:
			if not member.nid_front:
				exception = iam.MandatoryTableFieldError(targets={"target": "nid_front", "target_table": "board_members"}, row_idx=member.idx)
				iam.throw(_(parent_ngo.ERRORS.get("missing_board_member_nid_front")).format(member.idx), exception=exception)
			if not member.nid_back:
				exception = iam.MandatoryTableFieldError(targets={"target": "nid_back", "target_table": "board_members"}, row_idx=member.idx)
				iam.throw(_(parent_ngo.ERRORS.get("missing_board_member_nid_back")).format(member.idx), exception=exception)

	def validate_beneficiaries(self):
		# Validates that all Beneficiaries are unique, have valid IDs and have no active loans
		unique_beneficiaries = set()
		for beneficiary in self.beneficiaries:
			if len(beneficiary.nid) != 14:
				exception = iam.InvalidNIDTableFieldError(targets={"target": "nid", "target_table": "beneficiaries"}, row_idx=beneficiary.idx)
				iam.throw(_(parent_ngo.ERRORS.get("invalid_lr_beneficiary_nid")).format(beneficiary.idx), exception=exception)
			elif beneficiary.nid in unique_beneficiaries:
				exception = parent_ngo.DuplicateBeneficiaryError(targets={"target":"nid", "target_table":"beneficiaries"}, row_idx=beneficiary.idx)
				iam.throw(_(parent_ngo.ERRORS.get("duplicate_beneficiaries")).format(beneficiary.idx), exception=exception)
			elif self.validate_beneficiary_active_loans(beneficiary.nid):
				exception = parent_ngo.BeneficiaryInProgressError(targets={"target":"nid", "target_table":"beneficiaries"}, row_idx=beneficiary.idx)
				iam.throw(_(parent_ngo.ERRORS.get("beneficiary_existing_active_loans")).format(beneficiary.idx), exception=exception)
			else:
				unique_beneficiaries.add(beneficiary.nid)

	def validate_beneficiary_active_loans(self, nid):
		# Validates if a beneficiary has current active loans or loan requests in the same loan program
		exists = frappe.db.exists("Beneficiary Loans", {
			"parenttype": "Beneficiary",
			"parent": nid,
			"status": ["not in", ["Rejected", "Closed"]],
			"loan_request": ["!=", self.name],
			"loan_program": self.loan_program
		})
		return exists

	def validate_loan_program_criteria(self):
		# Validates if the requested loan amount is complying with the loan program criteria
		if not (
			self.requested_loan_amount >= self.loan_program_doc.min_amount and
			self.requested_loan_amount <= self.loan_program_doc.max_amount
		):
			exception = parent_ngo.LoanProgramAmountCriteriaMismatchError()
			iam.throw(_(parent_ngo.ERRORS.get("invalid_loan_program_criteria")), exception=exception)

	def validate_attachments(self):
		# Validates if all attachments are uploaded and set
		if self.status in ["Draft", "Pending CN Review"]:
			return
		# Validates that the uploaded attachments matches the "Loan Request Attachments"
		# read required_attachments from the "Loan Request Attachments" single doctype
		loan_request_attachments = frappe.get_single("Loan Request Attachments")
		required_attachments = loan_request_attachments.required_attachments
		uploaded_attachments_names = {attachment.name1 for attachment in self.uploaded_attachments if attachment.attachment}
		missing_attachments = [attachment.name1 for attachment in required_attachments if attachment.name1 not in uploaded_attachments_names]
		if missing_attachments:
			exception = parent_ngo.MissingRequiredAttachmentsError(targets={"missing_attachments": ", ".join(missing_attachments)})
			msg = _(parent_ngo.ERRORS.get("missing_required_attachments"))
			iam.throw(msg.format("<br>".join([''] + missing_attachments)), exception=exception)

	def validate_terms(self):
		# Validates if all terms are accepted
		if not self.accept_terms:
			exception = parent_ngo.TermsAndConditionsNotAcceptedError()
			iam.throw(_(parent_ngo.ERRORS.get("invalid_accept_terms")), exception=exception)

	def validate_review(self):
		# Validates all fields under the review tab
		if self._doc_before_save and not (self._doc_before_save.status == self.status):
			self.validate_iscore()
			self.validate_iscore_result()
			self.validate_field_investigations()
			self.validate_hc_report()

	def validate_iscore(self):
		# Validates if i-Score fields are set for treasurer, CEO, and all board members
		if self._doc_before_save.status == "Pending i-Score":
			# Validate treasurer and CEO iScore fields
			if not (self.treasurer_iscore_report and self.treasurer_iscore_report_id and self.ceo_iscore_report and self.ceo_iscore_report_id and self.treasurer_iscore_result is not None and self.ceo_iscore_result is not None):
				exception = parent_ngo.IncompleteIScoreDataError()
				iam.throw(_(parent_ngo.ERRORS.get("incomplete_iscore_data")), exception=exception)

			# Validate board members iScore fields
			for member in self.board_members:
				if not (member.bm_iscore_report and member.bm_iscore_report_id and member.bm_iscore_result is not None):
					exception = parent_ngo.IncompleteIScoreDataError()
					iam.throw(_(parent_ngo.ERRORS.get("incomplete_iscore_data")), exception=exception)

	def validate_iscore_result(self):
		# Validates if i-Score result is at least 600 for treasurer, CEO, and all board members
		minimum_iscore_result = self.loan_program_doc.minimum_iscore_result
		if self._doc_before_save.status == "Pending i-Score":
			# Validate treasurer and CEO iScore result
			if (self.treasurer_iscore_result < minimum_iscore_result) or (self.ceo_iscore_result < minimum_iscore_result):
				self.status = "Rejected"
				frappe.msgprint(_("Loan Request has been automatically rejected due to i-Score result for Treasurer or CEO is below {0}").format(minimum_iscore_result))


	def validate_field_investigations(self):
		# Validates if financial & administrative investigations fields are valid
		if self._doc_before_save.status == "Pending Field Investigation":
			self.validate_investigations("financial_investigations", "Financial")
			self.validate_investigations("administrative_investigations", "Administrative")

	def validate_investigations(self, field, investigation_type):
		# Validates if field investigations fields are set
		# and if there are no Opened or In Progress investigations
		# and if there is at least one completed investigation
		has_completed = False
		has_current = False

		for investigation in self.get(field):
			if investigation.status == "Completed":
				has_completed = True
			elif investigation.status in ["Opened", "In Progress"]:
				has_current = True

		if not self.get(field) or not has_completed or has_current:
			exception = parent_ngo.IncompleteInvestigationError(investigation_type=investigation_type)
			iam.throw(_(parent_ngo.ERRORS.get(f"incomplete_{field}")), exception=exception)

	def validate_hc_report(self):
		# Validates if the Higher Committee Report is uploaded
		if not self.hc_report and self._doc_before_save.status == "Pending HC Review":
			exception = iam.MandatoryFieldError(targets="hc_report")
			iam.throw(_(parent_ngo.ERRORS.get("missing_hc_report")), exception=exception)


	def validate_contract_details(self):
		# validate that the disbursements table is filled
		if self._doc_before_save and self._doc_before_save.status == "Pending HC Review":
			if not self.loan_disbursements:
				exception = iam.MandatoryFieldError(targets="loan_disbursements")
				iam.throw(_(parent_ngo.ERRORS.get("missing_loan_disbursements")), exception=exception)
			else:
				# validate that maximum number of future disbursements is 4 as requested 
				MAX_DISBURSEMENTS_NUM = 4
				num_of_disbursements = len(self.loan_disbursements)
				if num_of_disbursements > MAX_DISBURSEMENTS_NUM:
					exception = parent_ngo.DisbursementsMaxNumExceededError(targets={"max_disbursements_num": MAX_DISBURSEMENTS_NUM})
					iam.throw(_(parent_ngo.ERRORS.get("max_disbursements_num_exceeded")).format(MAX_DISBURSEMENTS_NUM), exception=exception)
				
    			# validate that the total disbursements amount equals the requested loan amount
				total_disbursements_amount = sum([d.disbursement_amount for d in self.loan_disbursements])
				if total_disbursements_amount != self.requested_loan_amount:
					exception = parent_ngo.TotalDisbursementsNotEqualLoanAmountError()
					iam.throw(_(parent_ngo.ERRORS.get("total_disbursements_not_equal_loan_amount")), exception=exception)
				# Complete the disbursements count to 4 if not 4
				if num_of_disbursements < MAX_DISBURSEMENTS_NUM:
					for i in range(MAX_DISBURSEMENTS_NUM - num_of_disbursements):
						self.append("loan_disbursements", {
							"disbursement_amount": 0
						})

	def set_ngo_members_attachments(self):
		# Sets the NGO Members attachments inside the Customer NGO Document
		if self.status in ["Draft", "Pending CN Review"]:
			return
		self.ngo_doc.set_ngo_members_attachments({
			"contact_nid_front": self.contact_nid_front,
			"contact_nid_back": self.contact_nid_back,
			"treasurer_nid_front": self.treasurer_nid_front,
			"treasurer_nid_back": self.treasurer_nid_back,
			"ceo_nid_front": self.ceo_nid_front,
			"ceo_nid_back": self.ceo_nid_back,
		}, self.name)

		self.ngo_doc.set_ngo_board_members_attachments(self.board_members, self.name)
		self.ngo_doc.save()

	def set_beneficiary_documents(self):
		# Sets the beneficiary details if their NID exists
		if self.is_new():
			return
		for beneficiary in self.beneficiaries:
			details = self.get_beneficiary_by_nid(beneficiary.nid)
			if details:
				beneficiary.beneficiary_doc = details.name
				beneficiary.nid = details.nid
				beneficiary.full_name = details.full_name
				beneficiary.child_doc = self.get_beneficiary_child_doc(details.name)
			else:
				beneficiary.beneficiary_doc = None
				beneficiary.child_doc = None
			beneficiary_data = {
				"beneficiary_doc": beneficiary.beneficiary_doc,
				"nid": beneficiary.nid,
				"full_name": beneficiary.full_name,
				"child_doc": beneficiary.child_doc,
				"status": self.loan_status or self.status,
				"loan_program": self.loan_program,
				"loan_request": self.name,
				"customer_ngo": self.ngo,
				"loan_amount": beneficiary.loan_amount,
				"project_description": beneficiary.project_description,
				"governorate": beneficiary.governorate
			}
			beneficiary.beneficiary_doc, beneficiary.child_doc = insert_beneficiary(**beneficiary_data)

	def get_beneficiary_child_doc(self, parent_id):
		# Gets the loan request entry in the child table of the beneficiary
		child_doc = frappe.db.get_value("Beneficiary Loans", {
			"loan_request": self.name,
			"customer_ngo": self.ngo,
			"parenttype": "Beneficiary",
			"parent": parent_id
		})
		return child_doc

	def delete_beneficiary_documents(self):
		# Deletes all the removed beneficiaries documents from the child table of the beneficiary
		if self.is_new():
			return
		existing_beneficiaries = [beneficiary.name for beneficiary in self.beneficiaries if beneficiary.name and not beneficiary.is_new()]
		removed_beneficiaires = frappe.db.get_all(
			"LR Beneficiary",
			filters = {
				"name": ["not in", existing_beneficiaries],
				"parenttype": "Loan Request",
				"parent": self.name,
				"parentfield": "beneficiaries",
				"child_doc": ["is", "set"]
			},
			pluck = "child_doc"
		)
		for removed_beneficiary in removed_beneficiaires:
			frappe.delete_doc("Beneficiary Loans", removed_beneficiary, True, ignore_permissions=True)

	@frappe.whitelist()
	def create_loan(self, save=False):
		# Create and submit a Loan Document linked with the current approved loan request
		if not save and (not self.status == "Approved" or self._doc_before_save.status == self.status):
			return
		loan = frappe.new_doc("Loan")
		loan.loan_request = self.name
		loan.save()
		loan.submit()
		self.loan = loan.name
		self.loan_status = loan.status
		if save:
			self.save()
		return loan.name

	def share_with_self(self):
		# Share the Loan Request with the Customer NGO User
		if not self.ngo_doc.user:
			return
		share.add_docshare(self.doctype, self.name, self.ngo_doc.user, write=1, flags={"ignore_share_permission": True})

	def submit_iscore_requests(self):
		# Submits the i-Score requests if the loan request is in Pending i-Score status
		if self._doc_before_save and self._doc_before_save.status != "Pending i-Score" and self.status == "Pending i-Score":
			# Submit treasurer and CEO iScore requests
			if self.treasurer_iscore_doc and self.ceo_iscore_doc:
				frappe.get_doc("iScore", self.treasurer_iscore_doc).submit()
				frappe.get_doc("iScore", self.ceo_iscore_doc).submit()

			# Submit board members iScore requests
			for member in self.board_members:
				if member.bm_iscore_doc:
					frappe.get_doc("iScore", member.bm_iscore_doc).submit()

	@frappe.whitelist()
	def set_customer_ngo(self):
		# Sets the customer NGO to the current session user NGO
		if not self.ngo:
			self.ngo = frappe.db.get_value("Customer NGO", {"contact_email": frappe.session.user})
			self.fetch_ngo_members()

	@frappe.whitelist()
	def fetch_ngo_members(self):
		# Fetches & Sets all NGO Members and their attachments
		if not self.ngo:
			return
		self.set_docs()
		self.set_terms()
		self.set_ngo_details()
		self.set_board_members()

	def set_ngo_details(self):
		# Sets NGO & NGO Members details
		self.ngo_name = self.ngo_doc.ngo_name
		self.publication_number = self.ngo_doc.publication_number
		self.license_number = self.ngo_doc.license_number
		self.address = self.ngo_doc.ngo_address
		self.email = self.ngo_doc.ngo_email
		self.publication_date = self.ngo_doc.publication_date
		self.license_date = self.ngo_doc.license_date
		self.contact_full_name = self.ngo_doc.contact_full_name
		self.contact_email = self.ngo_doc.contact_email
		self.contact_nid = self.ngo_doc.contact_nid
		self.contact_mobile_no = self.ngo_doc.contact_mobile_no
		self.treasurer_full_name = self.ngo_doc.treasurer_full_name
		self.treasurer_nid = self.ngo_doc.treasurer_nid
		self.ceo_full_name = self.ngo_doc.ceo_full_name
		self.ceo_nid = self.ngo_doc.ceo_nid

	@frappe.whitelist()
	def fetch_ngo_members_attachments(self):
		# Fetches & Sets all NGO Members attachments
		self.set_docs()
		self.clear_ngo_members()
		self.set_members_attachments()
		self.set_ngo_details()
		self.set_board_members(True)

	def set_members_attachments(self):
		# Sets Contact Person & Treasurer attachments
		if self.is_new():
			return
		attachment_fields = ["contact_nid_front", "contact_nid_back", "treasurer_nid_front", "treasurer_nid_back", "ceo_nid_front", "ceo_nid_back"]
		for field in attachment_fields:
			self.set(field, self.copy_attachment("Customer NGO", self.ngo, field, self.ngo_doc.get(field)))

	def set_board_members(self, fetch_attachments=False):
		# Sets Board Members and their attachments
		self.board_members = None
		for member in self.ngo_doc.board_members:
			if not self.is_new() and fetch_attachments:
				member_attachments = {
					"nid_front": self.copy_attachment("Customer NGO", self.ngo, "nid_front", member.nid_front),
					"nid_back": self.copy_attachment("Customer NGO", self.ngo, "nid_back", member.nid_back),
				}
			else:
				member_attachments = {}

			self.append("board_members", {
				"full_name": member.full_name,
				"nid": member.nid,
				"mobile_no": member.mobile_no,
				"ngo_occupation": member.ngo_occupation,
				**member_attachments
			})

	def copy_attachment(self, doctype, doc, field, file_url):
		# Gets a copy of an existing file document and creates a new one based on it
		file = frappe.db.get_value("File", {
			"attached_to_doctype": doctype,
			"attached_to_name": doc,
			"attached_to_field": field,
			"file_url": file_url,
		}, "name", order_by="creation desc")
		if file:
			attachment_doc = frappe.copy_doc(frappe.get_doc("File", file))
			attachment_doc.attached_to_doctype = self.doctype
			attachment_doc.attached_to_name = self.name
			attachment_doc.attached_to_field = field
			attachment_doc.save()
			return attachment_doc.file_url
		return None

	@frappe.whitelist()
	def clear_ngo_members(self):
		# Clears all NGO Members and their attachments
		self.clear_ngo_details()
		self.clear_members_attachments()
		self.clear_board_members_attachments()

	def clear_ngo_details(self):
		# Clears NGO & NGO Members details
		self.ngo_name = self.publication_number = self.license_number = self.address = self.email = self.publication_date = self.license_date = self.contact_full_name = self.contact_email = self.contact_nid = self.contact_mobile_no = self.treasurer_full_name = self.treasurer_nid = self.ceo_full_name = self.ceo_nid = None

	def clear_members_attachments(self):
		# Clears Contact Person & Treasurer attachments
		attachment_fields = ["contact_nid_front", "contact_nid_back", "treasurer_nid_front", "treasurer_nid_back","ceo_nid_front", "ceo_nid_back"]
		for field in attachment_fields:
			self.delete_attachment(field, self.get(field))
			self.set(field, None)

	def clear_board_members_attachments(self):
		# Clears Board Members and their attachments
		for member in self.board_members:
			self.delete_attachment("nid_front", member.nid_front)
			self.delete_attachment("nid_back", member.nid_back)
		self.board_members = None

	def delete_attachment(self, field, file_url=None):
		# Deletes an existing file document using its file_url
		if not file_url:
			return
		file = frappe.db.get_value("File", {
			"attached_to_doctype": self.doctype,
			"attached_to_name": self.name,
			"attached_to_field": field,
			"file_url": file_url,
		}, "name", order_by="creation desc")
		if file:
			frappe.delete_doc("File", file)

	@frappe.whitelist()
	def get_beneficiary_by_nid(self, nid):
		# Gets a Beneficiary by its National ID
		return frappe.db.get_value("Beneficiary", {"nid": nid}, ["name", "nid", "full_name"], as_dict=True)

	@frappe.whitelist()
	def has_field_investigation_permissions(self):
		# Checks if the current user has create permissions for field investigation documents
		return frappe.has_permission("Financial Investigation", "create") and frappe.has_permission("Administrative Investigation", "create")

	@frappe.whitelist()
	def create_investigation(self, investigation, investigator):
		# Creates a new Field Investigation document
		if self.get("__unsaved"):
			exception = iam.DocumentNotSavedError()
			iam.throw(_(parent_ngo.ERRORS.get("not_saved_doc")), exception=exception)

		field_investigation = frappe.get_doc({
			"doctype": investigation,
			"investigator": investigator,
			"loan_request": self.name
		})
		field_investigation.save()

	@frappe.whitelist()
	def delete_investigation(self, investigation):
		# Deletes the current Opened or In Progress Field Investigation document
		current_investigation = None
		if investigation == "Financial Investigation":
			investigations = self.financial_investigations
		elif investigation == "Administrative Investigation":
			investigations = self.administrative_investigations

		for field_investigation in investigations:
			if field_investigation.status in ["Opened", "In Progress"]:
				current_investigation = field_investigation.investigation
				break
		frappe.get_doc(investigation, current_investigation).delete()

	@frappe.whitelist()
	def request_iscore(self, inquiry_loan_type, inquiry_reason):
		# Validate if the User has the necessary permissions to request i-Score
		if not has_role("iScore Editor"):
			exception = iam.InsufficientRolesError()
			iam.throw(_(parent_ngo.ERRORS.get("no_permission")), exception=exception)
		# Requests i-Score for treasurer, CEO, and all board members
		try:
			# Request treasurer i-Score
			treasurer_iscore_doc = frappe.new_doc("iScore")
			treasurer_iscore_doc.ref_doctype = self.doctype
			treasurer_iscore_doc.ref_doc = self.name
			treasurer_iscore_doc.report_field = "treasurer_iscore_report"
			treasurer_iscore_doc.score_field = "treasurer_iscore_result"
			treasurer_iscore_doc.loans_number_field = "treasurer_iscore_loans_number"
			treasurer_iscore_doc.total_balance_field = "treasurer_iscore_total_balance"
			treasurer_iscore_doc.status_field = "treasurer_iscore_status"
			treasurer_iscore_doc.report_id_field = "treasurer_iscore_report_id"
			treasurer_iscore_doc.identifier_value = self.treasurer_nid
			treasurer_iscore_doc.beneficairy_name = self.treasurer_full_name
			treasurer_iscore_doc.inquiry_loan_type = inquiry_loan_type
			treasurer_iscore_doc.inquiry_reason = inquiry_reason
			treasurer_iscore_doc.save()

			self.treasurer_iscore_status = "Pending"
			self.treasurer_iscore_doc = treasurer_iscore_doc.name

			# Request CEO i-Score
			ceo_iscore_doc = frappe.new_doc("iScore")
			ceo_iscore_doc.ref_doctype = self.doctype
			ceo_iscore_doc.ref_doc = self.name
			ceo_iscore_doc.report_field = "ceo_iscore_report"
			ceo_iscore_doc.score_field = "ceo_iscore_result"
			ceo_iscore_doc.loans_number_field = "ceo_iscore_loans_number"
			ceo_iscore_doc.total_balance_field = "ceo_iscore_total_balance"
			ceo_iscore_doc.status_field = "ceo_iscore_status"
			ceo_iscore_doc.report_id_field = "ceo_iscore_report_id"
			ceo_iscore_doc.identifier_value = self.ceo_nid
			ceo_iscore_doc.beneficairy_name = self.ceo_full_name
			ceo_iscore_doc.inquiry_loan_type = inquiry_loan_type
			ceo_iscore_doc.inquiry_reason = inquiry_reason
			ceo_iscore_doc.save()

			self.ceo_iscore_status = "Pending"
			self.ceo_iscore_doc =  ceo_iscore_doc.name

			# Request board members i-Score
			self.request_board_members_iscore(inquiry_loan_type, inquiry_reason)

			self.db_update()
		except Exception:
			frappe.log_error(title="i-Score Request Failed")
			return False
		else:
			return True

	def request_board_members_iscore(self, inquiry_loan_type, inquiry_reason):
		# Requests i-Score for all board members
		for member in self.board_members:
			# Create iScore document for each board member
			member_iscore_doc = frappe.new_doc("iScore")
			member_iscore_doc.ref_doctype = member.doctype
			member_iscore_doc.ref_doc = member.name
			# Use a special field naming convention for child table fields
			member_iscore_doc.report_field = "bm_iscore_report"
			member_iscore_doc.score_field = "bm_iscore_result"
			member_iscore_doc.loans_number_field = "bm_iscore_loans_number"
			member_iscore_doc.total_balance_field = "bm_iscore_total_balance"
			member_iscore_doc.status_field = "bm_iscore_status"
			member_iscore_doc.report_id_field = "bm_iscore_report_id"
			member_iscore_doc.identifier_value = member.nid
			member_iscore_doc.beneficairy_name = member.full_name
			member_iscore_doc.inquiry_loan_type = inquiry_loan_type
			member_iscore_doc.inquiry_reason = inquiry_reason
			member_iscore_doc.save()

			# Update board member iScore status and doc reference
			frappe.db.set_value(member.doctype, member.name, {
				"bm_iscore_status": "Pending",
				"bm_iscore_doc": member_iscore_doc.name
			})