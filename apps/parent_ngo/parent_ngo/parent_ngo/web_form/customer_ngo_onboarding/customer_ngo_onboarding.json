{"accept_payment": 0, "allow_comments": 0, "allow_delete": 0, "allow_edit": 0, "allow_incomplete": 1, "allow_multiple": 0, "allow_print": 0, "amount": 0.0, "amount_based_on_field": 0, "anonymous": 0, "apply_document_permissions": 0, "button_label": "Save", "condition_json": "[]", "creation": "2024-03-05 16:33:21.001572", "currency": "EGP", "custom_css": "html[lang=\"ar\"] {\n    direction: rtl;\n}", "doc_type": "Customer NGO", "docstatus": 0, "doctype": "Web Form", "idx": 0, "is_standard": 1, "list_columns": [], "login_required": 0, "max_attachment_size": 0, "modified": "2025-08-06 17:42:18.757310", "modified_by": "Administrator", "module": "Parent NGO", "name": "customer-ngo-onboarding", "owner": "Administrator", "payment_button_label": "Buy Now", "published": 1, "route": "customer-ngo-onboarding", "show_attachments": 0, "show_list": 0, "show_sidebar": 0, "success_message": "Your NGO Has Been Registered Successfully", "success_title": "Thanks", "title": "Customer NGO Onboarding", "web_form_fields": [{"allow_read_on_all_link_options": 0, "fieldname": "ngo_name", "fieldtype": "Data", "hidden": 0, "label": "Name", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "publication_number", "fieldtype": "Data", "hidden": 0, "label": "Publication Number", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "license_number", "fieldtype": "Data", "hidden": 0, "label": "License Number", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ngo_address", "fieldtype": "Data", "hidden": 0, "label": "Address", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "column_break_kx5zr", "fieldtype": "Column Break", "hidden": 0, "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ngo_email", "fieldtype": "Data", "hidden": 0, "label": "Email", "max_length": 0, "max_value": 0, "options": "Email", "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "publication_date", "fieldtype": "Date", "hidden": 0, "label": "Publication Date", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "license_date", "fieldtype": "Date", "hidden": 0, "label": "License Date", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "contact_information_tab", "fieldtype": "Page Break", "hidden": 0, "label": "Contact Information", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "contact_full_name", "fieldtype": "Data", "hidden": 0, "label": "Full Name", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "contact_email", "fieldtype": "Data", "hidden": 0, "label": "Email", "max_length": 0, "max_value": 0, "options": "Email", "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "contact_position", "fieldtype": "Data", "hidden": 0, "label": "Position/Title", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "column_break_e0i2t", "fieldtype": "Column Break", "hidden": 0, "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "contact_nid", "fieldtype": "Data", "hidden": 0, "label": "National ID", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "contact_mobile_no", "fieldtype": "Data", "hidden": 0, "label": "Mobile Number", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "treasurer_tab", "fieldtype": "Page Break", "hidden": 0, "label": "Treasurer", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "treasurer_full_name", "fieldtype": "Data", "hidden": 0, "label": "Full Name", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "treasurer_nid", "fieldtype": "Data", "hidden": 0, "label": "National ID", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "treasurer_mobile_no", "fieldtype": "Data", "hidden": 0, "label": "Mobile Number", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "column_break_v2npw", "fieldtype": "Column Break", "hidden": 0, "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "treasurer_occupation", "fieldtype": "Data", "hidden": 0, "label": "Occupation Out of The NGO", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "treasurer_ngo_occupation", "fieldtype": "Data", "hidden": 0, "label": "Occupation Within The NGO", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "", "fieldtype": "Page Break", "hidden": 0, "label": "CEO <PERSON><PERSON>", "max_length": 0, "max_value": 0, "options": "", "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ceo_full_name", "fieldtype": "Data", "hidden": 0, "label": "Full Name", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ceo_nid", "fieldtype": "Data", "hidden": 0, "label": "National ID", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ceo_mobile_no", "fieldtype": "Data", "hidden": 0, "label": "Mobile Number", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "column_break_dmbr", "fieldtype": "Column Break", "hidden": 0, "label": "", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ceo_occupation", "fieldtype": "Data", "hidden": 0, "label": "Occupation Out of The NGO", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ceo_ngo_occupation", "fieldtype": "Data", "hidden": 0, "label": "Occupation Within The NGO", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ceo_attachments", "fieldtype": "Section Break", "hidden": 0, "label": "Attachments", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ceo_nid_front", "fieldtype": "Attach Image", "hidden": 0, "label": "National ID Front", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "column_break_jodz", "fieldtype": "Column Break", "hidden": 0, "label": "", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "ceo_nid_back", "fieldtype": "Attach Image", "hidden": 0, "label": "National ID Back", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "board_members_tab", "fieldtype": "Page Break", "hidden": 0, "label": "Board Members", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "board_members", "fieldtype": "Table", "hidden": 0, "label": "Board Members", "max_length": 0, "max_value": 0, "options": "Board Member Details", "precision": "", "read_only": 0, "reqd": 1, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "", "fieldtype": "Page Break", "hidden": 0, "label": "Attachments", "max_length": 0, "max_value": 0, "options": "", "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "required_attachments", "fieldtype": "Table", "hidden": 1, "label": "Required Attachments", "max_length": 0, "max_value": 0, "options": "Attachment", "precision": "", "read_only": 1, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "section_break_obff", "fieldtype": "Section Break", "hidden": 0, "label": "", "max_length": 0, "max_value": 0, "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}, {"allow_read_on_all_link_options": 0, "fieldname": "uploaded_attachments", "fieldtype": "Table", "hidden": 0, "label": "Uploaded Attachments", "max_length": 0, "max_value": 0, "options": "Attachment", "precision": "", "read_only": 0, "reqd": 0, "show_in_filter": 0}]}