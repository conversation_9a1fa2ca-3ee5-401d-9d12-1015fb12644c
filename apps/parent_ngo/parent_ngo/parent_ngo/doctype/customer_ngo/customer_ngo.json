{"actions": [], "allow_rename": 1, "autoname": "field:ngo_name", "creation": "2024-01-24 14:36:41.733795", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["ngo_details_tab", "status", "column_break_ukwq", "user", "details_section", "ngo_name", "publication_number", "license_number", "ngo_address", "column_break_kx5zr", "ngo_email", "publication_date", "license_date", "contact_information_tab", "contact_full_name", "contact_email", "contact_position", "column_break_e0i2t", "contact_nid", "contact_mobile_no", "contact_attachments", "contact_nid_front", "contact_nid_front_preview", "column_break_ddfc", "contact_nid_back", "contact_nid_back_preview", "treasurer_tab", "treasurer_section", "treasurer_full_name", "treasurer_nid", "treasurer_mobile_no", "column_break_v2npw", "treasurer_occupation", "treasurer_ngo_occupation", "treasurer_attachments", "treasurer_nid_front", "treasurer_nid_front_preview", "column_break_jubg", "treasurer_nid_back", "treasurer_nid_back_preview", "ceo_info_tab", "ceo_full_name", "ceo_nid", "ceo_mobile_no", "column_break_dmbr", "ceo_occupation", "ceo_ngo_occupation", "ceo_attachments", "ceo_nid_front", "ceo_nid_front_preview", "column_break_jodz", "ceo_nid_back", "ceo_nid_back_preview", "board_members_tab", "board_members", "attachments_tab", "required_attachments", "section_break_obff", "uploaded_attachments"], "fields": [{"fieldname": "ngo_details_tab", "fieldtype": "Tab Break", "label": "NGO Details"}, {"fieldname": "ngo_name", "fieldtype": "Data", "label": "Name", "reqd": 1, "unique": 1}, {"fieldname": "publication_date", "fieldtype": "Date", "in_list_view": 1, "label": "Publication Date", "reqd": 1}, {"fieldname": "publication_number", "fieldtype": "Data", "in_list_view": 1, "label": "Publication Number", "reqd": 1, "unique": 1}, {"fieldname": "column_break_kx5zr", "fieldtype": "Column Break"}, {"fieldname": "license_number", "fieldtype": "Data", "label": "License Number", "reqd": 1, "unique": 1}, {"fieldname": "license_date", "fieldtype": "Date", "label": "License Date", "reqd": 1}, {"fieldname": "ngo_address", "fieldtype": "Data", "label": "Address", "reqd": 1}, {"fieldname": "contact_information_tab", "fieldtype": "Tab Break", "label": "Contact Information"}, {"fieldname": "ngo_email", "fieldtype": "Data", "in_list_view": 1, "label": "Email", "options": "Email", "reqd": 1, "unique": 1}, {"fieldname": "column_break_e0i2t", "fieldtype": "Column Break"}, {"fieldname": "treasurer_section", "fieldtype": "Section Break"}, {"fieldname": "treasurer_nid", "fieldtype": "Data", "label": "National ID", "length": 14, "reqd": 1}, {"fieldname": "treasurer_occupation", "fieldtype": "Data", "label": "Occupation Out of The NGO", "reqd": 1}, {"fieldname": "column_break_v2npw", "fieldtype": "Column Break"}, {"fieldname": "contact_full_name", "fieldtype": "Data", "label": "Full Name", "reqd": 1}, {"fieldname": "contact_nid", "fieldtype": "Data", "label": "National ID", "length": 14, "reqd": 1}, {"fieldname": "contact_email", "fieldtype": "Data", "label": "Email", "options": "Email", "reqd": 1, "unique": 1}, {"fieldname": "contact_position", "fieldtype": "Data", "label": "Position/Title", "reqd": 1}, {"fieldname": "contact_mobile_no", "fieldtype": "Data", "label": "Mobile Number", "length": 11, "reqd": 1}, {"fieldname": "treasurer_full_name", "fieldtype": "Data", "label": "Full Name", "reqd": 1}, {"fieldname": "treasurer_mobile_no", "fieldtype": "Data", "label": "Mobile Number", "length": 11, "reqd": 1}, {"fieldname": "treasurer_ngo_occupation", "fieldtype": "Data", "label": "Occupation Within The NGO", "reqd": 1}, {"depends_on": "eval: !doc.__islocal", "fieldname": "contact_nid_front", "fieldtype": "Attach Image", "label": "National ID Front"}, {"depends_on": "contact_nid_front", "fieldname": "contact_nid_front_preview", "fieldtype": "Image", "options": "contact_nid_front"}, {"fieldname": "column_break_ddfc", "fieldtype": "Column Break"}, {"depends_on": "eval: !doc.__islocal", "fieldname": "contact_nid_back", "fieldtype": "Attach Image", "label": "National ID Back"}, {"depends_on": "contact_nid_back", "fieldname": "contact_nid_back_preview", "fieldtype": "Image", "options": "contact_nid_back"}, {"fieldname": "contact_attachments", "fieldtype": "Section Break", "label": "Attachments"}, {"fieldname": "treasurer_tab", "fieldtype": "Tab Break", "label": "Treasurer"}, {"fieldname": "treasurer_attachments", "fieldtype": "Section Break", "label": "Attachments"}, {"depends_on": "eval: !doc.__islocal", "fieldname": "treasurer_nid_front", "fieldtype": "Attach Image", "label": "National ID Front"}, {"depends_on": "treasurer_nid_front", "fieldname": "treasurer_nid_front_preview", "fieldtype": "Image", "options": "treasurer_nid_front"}, {"fieldname": "column_break_jubg", "fieldtype": "Column Break"}, {"depends_on": "eval: !doc.__islocal", "fieldname": "treasurer_nid_back", "fieldtype": "Attach Image", "label": "National ID Back"}, {"depends_on": "treasurer_nid_back", "fieldname": "treasurer_nid_back_preview", "fieldtype": "Image", "options": "treasurer_nid_back"}, {"fieldname": "board_members_tab", "fieldtype": "Tab Break", "label": "Board Members"}, {"fieldname": "board_members", "fieldtype": "Table", "label": "Board Members", "options": "Board Member Details", "reqd": 1}, {"default": "Pending", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Pending\nApproved\nRejected", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_ukwq", "fieldtype": "Column Break"}, {"fieldname": "details_section", "fieldtype": "Section Break", "label": "Details"}, {"fieldname": "user", "fieldtype": "Link", "label": "User", "no_copy": 1, "options": "User", "read_only": 1, "unique": 1}, {"fieldname": "ceo_info_tab", "fieldtype": "Tab Break", "label": "CEO <PERSON><PERSON>"}, {"fieldname": "ceo_full_name", "fieldtype": "Data", "label": "Full Name", "reqd": 1}, {"fieldname": "ceo_nid", "fieldtype": "Data", "label": "National ID", "length": 14, "reqd": 1}, {"fieldname": "ceo_mobile_no", "fieldtype": "Data", "label": "Mobile Number", "length": 11, "reqd": 1}, {"fieldname": "column_break_dmbr", "fieldtype": "Column Break"}, {"fieldname": "ceo_occupation", "fieldtype": "Data", "label": "Occupation Out of The NGO", "reqd": 1}, {"fieldname": "ceo_ngo_occupation", "fieldtype": "Data", "label": "Occupation Within The NGO", "reqd": 1}, {"fieldname": "ceo_attachments", "fieldtype": "Section Break", "label": "Attachments"}, {"fieldname": "ceo_nid_front", "fieldtype": "Attach Image", "label": "National ID Front"}, {"fieldname": "column_break_jodz", "fieldtype": "Column Break"}, {"fieldname": "ceo_nid_back", "fieldtype": "Attach Image", "label": "National ID Back"}, {"fieldname": "ceo_nid_front_preview", "fieldtype": "Image", "options": "ceo_nid_front"}, {"fieldname": "ceo_nid_back_preview", "fieldtype": "Image", "options": "ceo_nid_back"}, {"fieldname": "attachments_tab", "fieldtype": "Tab Break", "label": "Attachments"}, {"fieldname": "required_attachments", "fieldtype": "Table", "hidden": 1, "label": "Required Attachments", "options": "Attachment", "read_only": 1}, {"fieldname": "section_break_obff", "fieldtype": "Section Break"}, {"fieldname": "uploaded_attachments", "fieldtype": "Table", "label": "Uploaded Attachments", "options": "Attachment"}], "index_web_pages_for_search": 1, "links": [{"link_doctype": "Beneficiary", "link_fieldname": "customer_ngo"}], "modified": "2025-08-07 12:39:41.230644", "modified_by": "Administrator", "module": "Parent NGO", "name": "Customer NGO", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Officer", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Supervisor", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Loan Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "NGO Accountant", "share": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "translated_doctype": 1}