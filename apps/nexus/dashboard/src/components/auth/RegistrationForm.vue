<script setup>
import { ref, reactive, onMounted, watch, computed, defineAsyncComponent } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "../../stores/auth";
import onboardingService from "../../services/onboardingService";
import Spinner from "../ui/Spinner.vue";

// Lazy load heavy components
const LoadingState = defineAsyncComponent(() => import("../LoadingState.vue"));
const Alert = defineAsyncComponent(() => import("../ui/Alert.vue"));

const router = useRouter();
const authStore = useAuthStore();

// Form data
const formData = reactive({
	first_name: "",
	last_name: "",
	email: "",
	password: "",
	company_name: "",
	abbr: "",
	country: "",
	default_currency: "",
	language: "",
});

// Form state
const errors = reactive({});
const errorMessage = ref("");
const isLoadingData = ref(true);
const isSubmitting = ref(false);
const showPassword = ref(false);
const isRedirectingToLogin = ref(false);

// Dropdown data
const countries = ref([]);
const currencies = ref([]);
const languages = ref([]);

// Step management
const currentStep = ref(1);

// Dynamic step configuration generator
const generateSteps = () => {
	return [
		{
			id: 1,
			title: "Personal Information",
			description: "Tell us about yourself",
			icon: "user",
			fields: ["first_name", "last_name", "email", "password"],
		},
		{
			id: 2,
			title: "Company Details",
			description: "Setup your company",
			icon: "building",
			fields: ["company_name", "abbr", "country", "default_currency", "language"],
		},
	];
};

// Dynamic step configuration
const steps = ref(generateSteps());

const totalSteps = computed(() => steps.value?.length || 0);

// Get current step data
const currentStepData = computed(() => {
	if (!steps.value || steps.value.length === 0) {
		return null;
	}
	return steps.value.find((step) => step.id === currentStep.value) || steps.value[0];
});

// Check if form is ready to display
const isFormReady = computed(() => {
	return !isLoadingData.value && steps.value && steps.value.length > 0;
});

// Load dropdown data on component mount
onMounted(async () => {
	try {
		isLoadingData.value = true;

		// Check if user is already authenticated and skip to company creation step
		if (authStore.isLoggedIn()) {
			currentStep.value = 2; // Skip to company creation step
			console.log("User already authenticated, skipping to company creation step");
		}

		// Add timeout to prevent infinite loading
		const timeoutPromise = new Promise((_, reject) => {
			setTimeout(() => reject(new Error("Request timeout")), 10000); // 10 second timeout
		});

		// Load all dropdown data in parallel with timeout
		const [countriesData, currenciesData, languagesData] = await Promise.race([
			Promise.all([
				onboardingService.fetchCountries(),
				onboardingService.fetchCurrencies(),
				onboardingService.fetchLanguages(),
			]),
			timeoutPromise,
		]);

		countries.value = countriesData;
		currencies.value = currenciesData;
		languages.value = languagesData;
	} catch (error) {
		console.error("Error loading form data:", error);
		errorMessage.value = "Failed to load form data. Please refresh the page.";

		// Set fallback data to prevent infinite loading
		countries.value = [];
		currencies.value = [];
		languages.value = [];
	} finally {
		isLoadingData.value = false;
	}
});

// Watch for company name changes and auto-generate abbreviation
watch(
	() => formData.company_name,
	(newValue) => {
		if (newValue && newValue.trim()) {
			generateAbbreviation();
		}
	}
);

// Toggle password visibility
const togglePasswordVisibility = () => {
	showPassword.value = !showPassword.value;
};

// Step navigation functions
const nextStep = async () => {
	// Reset redirecting state when starting a new action
	isRedirectingToLogin.value = false;

	if (validateCurrentStep()) {
		const currentStepData = steps.value.find((step) => step.id === currentStep.value);

		// Handle step-specific actions
		if (currentStep.value === 1) {
			await handleUserCreation();
		} else if (currentStep.value === 2) {
			await handleCompanyCreation();
		} else if (currentStep.value < totalSteps.value) {
			// For future steps, just move to next step
			currentStep.value++;
		}
	}
};

const prevStep = () => {
	// Prevent authenticated users from going back to step 1 (user creation)
	const minStep = authStore.isLoggedIn() ? 2 : 1;
	if (currentStep.value > minStep) {
		currentStep.value--;
	}
};

const goToStep = (step) => {
	// Prevent authenticated users from accessing step 1 (user creation)
	const minStep = authStore.isLoggedIn() ? 2 : 1;
	if (step >= minStep && step <= totalSteps.value) {
		currentStep.value = step;
	}
};

// Generate company abbreviation from company name
const generateAbbreviation = (force = false) => {
	if (formData.company_name) {
		const words = formData.company_name.trim().split(/\s+/);
		let abbr = "";

		if (words.length === 1) {
			// Single word: take first 2-3 characters
			abbr = words[0].substring(0, 3).toUpperCase();
		} else {
			// Multiple words: take first letter of each word (max 5)
			abbr = words
				.slice(0, 5)
				.map((word) => word.charAt(0))
				.join("")
				.toUpperCase();
		}

		// Update if forced, current abbreviation is empty, or was auto-generated
		if (force || !formData.abbr || formData.abbr === getLastGeneratedAbbr()) {
			formData.abbr = abbr;
			setLastGeneratedAbbr(abbr);
		}
	}
};

// Track the last auto-generated abbreviation to allow re-generation
let lastGeneratedAbbr = "";

const getLastGeneratedAbbr = () => lastGeneratedAbbr;
const setLastGeneratedAbbr = (abbr) => {
	lastGeneratedAbbr = abbr;
};

// Step validation
const validateCurrentStep = () => {
	// Safety check for steps
	if (!steps.value || !steps.value[currentStep.value - 1]) {
		return false;
	}

	// Clear previous errors for current step
	const currentStepFields = steps.value[currentStep.value - 1].fields;
	currentStepFields.forEach((field) => delete errors[field]);

	let isValid = true;

	// Validate fields for current step
	currentStepFields.forEach((field) => {
		if (!formData[field] || formData[field].trim() === "") {
			errors[field] = `${field.replace("_", " ")} is required`;
			isValid = false;
		}
	});

	// Additional validations
	if (currentStepFields.includes("email") && formData.email) {
		if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = "Please enter a valid email address";
			isValid = false;
		}
	}

	if (currentStepFields.includes("password") && formData.password) {
		if (formData.password.length < 8) {
			errors.password = "Password must be at least 8 characters long";
			isValid = false;
		}
	}

	if (currentStepFields.includes("abbr") && formData.abbr) {
		if (formData.abbr.length > 5) {
			errors.abbr = "Abbreviation must be 5 characters or less";
			isValid = false;
		}
	}

	return isValid;
};

// Form validation (for final submission)
const validateForm = () => {
	// Clear previous errors
	Object.keys(errors).forEach((key) => delete errors[key]);

	let isValid = true;

	// Required field validation
	const requiredFields = [
		"first_name",
		"last_name",
		"email",
		"password",
		"company_name",
		"abbr",
		"country",
		"default_currency",
		"language",
	];

	requiredFields.forEach((field) => {
		if (!formData[field] || formData[field].trim() === "") {
			errors[field] = `${field.replace("_", " ")} is required`;
			isValid = false;
		}
	});

	// Email validation
	if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
		errors.email = "Please enter a valid email address";
		isValid = false;
	}

	// Password validation
	if (formData.password && formData.password.length < 8) {
		errors.password = "Password must be at least 8 characters long";
		isValid = false;
	}

	// Abbreviation validation
	if (formData.abbr && formData.abbr.length > 5) {
		errors.abbr = "Abbreviation must be 5 characters or less";
		isValid = false;
	}

	return isValid;
};

// Handle user creation (step 1)
const handleUserCreation = async () => {
	isSubmitting.value = true;
	errorMessage.value = "";

	try {
		// Extract user data from form
		const userData = {
			email: formData.email,
			first_name: formData.first_name,
			last_name: formData.last_name,
			password: formData.password,
		};

		const response = await onboardingService.createUser(userData);

		if (response.success) {
			// Store user data in auth store if provided
			if (response.data?.user) {
				authStore.setAuthData({
					user: response.data.user,
					sessionId: response.data.auth?.session_id,
				});
			}

			// Move to next step
			currentStep.value++;
		} else {
			errorMessage.value =
				response.user_message || "User creation failed. Please try again.";
		}
	} catch (error) {
		console.error("User creation error:", error);

		// Handle specific error responses
		if (
			error.message === "UserExistsError" ||
			error.response?.data?.exc_type === "UserExistsError" ||
			error.response?.status === 409
		) {
			// User already exists - show message and redirect to login
			errorMessage.value =
				"An account with this email already exists. Redirecting to login...";
			isRedirectingToLogin.value = true;

			// Redirect to login after a short delay
			setTimeout(() => {
				router.push("/dashboard/login");
			}, 2000);
		} else if (error.response?.data?.user_message) {
			errorMessage.value = error.response.data.user_message;
		} else if (error.response?.data?.message) {
			errorMessage.value = error.response.data.message;
		} else if (error.message) {
			errorMessage.value = error.message;
		} else {
			errorMessage.value =
				"User creation failed. Please check your information and try again.";
		}
	} finally {
		isSubmitting.value = false;
	}
};

// Handle company creation (step 2)
const handleCompanyCreation = async () => {
	isSubmitting.value = true;
	errorMessage.value = "";

	try {
		// Extract all company data including preferences for step 2
		const companyData = {
			company_name: formData.company_name,
			abbr: formData.abbr,
			country: formData.country,
			default_currency: formData.default_currency,
			language: formData.language,
		};

		console.log("Creating company with data:", companyData);

		const response = await onboardingService.createCompany(companyData);

		console.log("Company creation response:", response);

		if (response.success) {
			// Company created successfully, handle authentication and redirect
			if (response.data?.auth?.session_id) {
				// Store auth data if provided
				authStore.setAuthData({
					user: response.data.user,
					sessionId: response.data.auth.session_id,
				});
			}

			// Refresh company status to reflect the newly created company
			try {
				await authStore.refreshCompanyStatus();
			} catch (error) {
				console.warn("Failed to refresh company status after creation:", error);
			}

			// Redirect to dashboard
			router.push("/dashboard");
		} else {
			errorMessage.value =
				response.user_message ||
				response.message ||
				"Company creation failed. Please try again.";
		}
	} catch (error) {
		console.error("Company creation error:", error);

		// Handle specific error responses
		if (error.response?.data?.user_message) {
			errorMessage.value = error.response.data.user_message;
		} else if (error.response?.data?.message) {
			errorMessage.value = error.response.data.message;
		} else if (error.message) {
			errorMessage.value = error.message;
		} else {
			errorMessage.value =
				"Company creation failed. Please check your information and try again.";
		}
	} finally {
		isSubmitting.value = false;
	}
};
</script>

<template>
	<div
		class="bg-white dark:bg-gray-900 p-4 sm:p-6 md:p-8 rounded-2xl shadow-2xl w-full max-w-sm sm:max-w-md md:max-w-2xl lg:max-w-4xl mx-auto border border-gray-200 dark:border-gray-800 backdrop-blur-sm"
	>
		<!-- Logo and Header -->
		<div class="text-center mb-6 sm:mb-8">
			<div class="flex justify-center mb-3 sm:mb-4">
				<div class="relative">
					<img
						src="/logo.png"
						alt="Nexus Logo"
						class="h-8 sm:h-10 md:h-12 w-auto drop-shadow-lg"
					/>
					<div class="absolute inset-0 bg-blue-500/10 rounded-full blur-xl"></div>
				</div>
			</div>
			<h1
				class="text-xl sm:text-2xl md:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent"
			>
				{{ authStore.isLoggedIn() ? "Setup Your Company" : "Create Your Account" }}
			</h1>
			<p
				class="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-2 sm:mt-3 max-w-md mx-auto"
			>
				{{
					authStore.isLoggedIn()
						? "Complete your setup by creating your company profile"
						: "Join Nexus and start managing your business with our powerful platform"
				}}
			</p>
		</div>

		<!-- Progress Steps -->
		<div class="mb-6 sm:mb-8">
			<div class="flex items-center justify-center space-x-4 sm:space-x-8">
				<div
					v-for="(step, index) in steps"
					:key="step.id"
					v-show="!authStore.isLoggedIn() || step.id !== 1"
					class="flex items-center"
				>
					<!-- Step Circle -->
					<div class="flex items-center">
						<div
							class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 transition-all duration-300 shadow-lg"
							:class="{
								'bg-gradient-to-br from-blue-500 to-blue-600 border-blue-500 text-white shadow-blue-500/25':
									currentStep >= step.id,
								'border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800':
									currentStep < step.id,
								'bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 border-blue-500 text-blue-600 dark:text-blue-400 shadow-blue-500/20':
									currentStep === step.id,
							}"
						>
							<!-- Step Icon -->
							<svg
								v-if="step.icon === 'user'"
								class="w-4 h-4 sm:w-5 sm:h-5"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
								/>
							</svg>
							<svg
								v-else-if="step.icon === 'building'"
								class="w-4 h-4 sm:w-5 sm:h-5"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
								/>
							</svg>
							<svg
								v-else-if="step.icon === 'settings'"
								class="w-4 h-4 sm:w-5 sm:h-5"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
								/>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
								/>
							</svg>
							<span v-else class="text-sm font-medium">{{ step.id }}</span>
						</div>

						<!-- Step Info -->
						<div class="ml-2 sm:ml-3 hidden sm:block">
							<p
								class="text-xs sm:text-sm font-medium"
								:class="{
									'text-blue-600 dark:text-blue-400': currentStep === step.id,
									'text-gray-900 dark:text-white': currentStep > step.id,
									'text-gray-500 dark:text-gray-400': currentStep < step.id,
								}"
							>
								{{ step.title }}
							</p>
							<p class="text-xs text-gray-500 dark:text-gray-400">
								{{ step.description }}
							</p>
						</div>
					</div>

					<!-- Connector Line -->
					<div
						v-if="steps.value && index < steps.value.length - 1"
						class="relative w-12 sm:w-16 h-1.5 transition-all duration-500 ease-out"
					>
						<!-- Background line -->
						<div
							class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded-full"
						></div>

						<!-- Progress line -->
						<div
							class="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500 rounded-full transition-all duration-500 ease-out"
							:class="{
								'w-full': currentStep > step.id,
								'w-0': currentStep <= step.id,
							}"
						>
							<!-- Shimmer effect for completed steps -->
							<div
								v-if="currentStep > step.id"
								class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full animate-pulse"
								style="animation-duration: 2s"
							></div>
						</div>

						<!-- Connection dots -->
						<div
							class="absolute -top-0.5 left-0 w-2 h-2 bg-gray-200 dark:bg-gray-700 rounded-full border-2 border-white dark:border-gray-800"
						></div>
						<div
							class="absolute -top-0.5 right-0 w-2 h-2 bg-gray-200 dark:bg-gray-700 rounded-full border-2 border-white dark:border-gray-800"
						></div>
					</div>
				</div>
			</div>
		</div>

		<!-- Error Alert -->
		<Alert
			v-if="errorMessage"
			variant="destructive"
			:dismissible="!isRedirectingToLogin"
			class="mb-3 sm:mb-4"
		>
			<div class="flex items-center gap-2">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					stroke-width="2"
					stroke-linecap="round"
					stroke-linejoin="round"
					class="lucide lucide-alert-circle"
				>
					<circle cx="12" cy="12" r="10" />
					<line x1="12" x2="12" y1="8" y2="12" />
					<line x1="12" x2="12.01" y1="16" y2="16" />
				</svg>
				<span>{{ errorMessage }}</span>
			</div>

			<!-- Manual Login Button for UserExistsError -->
			<div v-if="isRedirectingToLogin" class="mt-3">
				<button
					@click="router.push('/dashboard/login')"
					class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 dark:text-red-300 dark:bg-red-900/30 dark:hover:bg-red-900/50 rounded-md transition-colors duration-200"
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						width="14"
						height="14"
						viewBox="0 0 24 24"
						fill="none"
						stroke="currentColor"
						stroke-width="2"
						stroke-linecap="round"
						stroke-linejoin="round"
						class="mr-1.5"
					>
						<path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" />
						<polyline points="10,17 15,12 10,7" />
						<line x1="15" y1="12" x2="3" y2="12" />
					</svg>
					Go to Login Now
				</button>
			</div>
		</Alert>

		<!-- Loading State -->
		<LoadingState v-if="isLoadingData" message="Loading form data..." />

		<!-- Step-based Registration Form -->
		<div v-else-if="isFormReady" class="min-h-[250px] sm:min-h-[300px]">
			<!-- Step Content Container -->
			<div class="transition-all duration-300 ease-in-out">
				<!-- Dynamic Step Content -->
				<div
					v-if="currentStep === 1 && currentStepData && !authStore.isLoggedIn()"
					class="space-y-3 sm:space-y-4"
				>
					<div class="space-y-2 sm:space-y-3">
						<div class="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
							<!-- First Name -->
							<div class="space-y-2">
								<label
									for="firstName"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									First Name *
								</label>
								<input
									id="firstName"
									v-model="formData.first_name"
									type="text"
									placeholder="Enter your first name"
									class="w-full px-4 py-3 border-2 rounded-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-sm shadow-sm"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500/20 dark:ring-red-500/20':
											errors.first_name,
									}"
								/>
								<p
									v-if="errors.first_name"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.first_name }}
								</p>
							</div>

							<!-- Last Name -->
							<div class="space-y-2">
								<label
									for="lastName"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Last Name *
								</label>
								<input
									id="lastName"
									v-model="formData.last_name"
									type="text"
									placeholder="Enter your last name"
									class="w-full px-4 py-3 border-2 rounded-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-sm shadow-sm"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500/20 dark:ring-red-500/20':
											errors.last_name,
									}"
								/>
								<p
									v-if="errors.last_name"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.last_name }}
								</p>
							</div>
						</div>

						<!-- Email -->
						<div class="space-y-2">
							<label
								for="email"
								class="text-sm font-medium text-gray-700 dark:text-gray-300"
							>
								Email Address *
							</label>
							<input
								id="email"
								v-model="formData.email"
								type="email"
								placeholder="Enter your email address"
								class="w-full px-4 py-3 border-2 rounded-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-sm shadow-sm"
								:class="{
									'border-red-500 dark:border-red-500 ring-red-500/20 dark:ring-red-500/20':
										errors.email,
								}"
							/>
							<p v-if="errors.email" class="text-sm text-red-500 dark:text-red-400">
								{{ errors.email }}
							</p>
						</div>

						<!-- Password -->
						<div class="space-y-2">
							<label
								for="password"
								class="text-sm font-medium text-gray-700 dark:text-gray-300"
							>
								Password *
							</label>
							<div class="relative">
								<input
									id="password"
									v-model="formData.password"
									:type="showPassword ? 'text' : 'password'"
									placeholder="Create a strong password"
									class="w-full px-4 pr-12 py-3 border-2 rounded-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-sm shadow-sm"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500/20 dark:ring-red-500/20':
											errors.password,
									}"
								/>
								<button
									type="button"
									@click="togglePasswordVisibility"
									class="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 focus:outline-none transition-colors duration-200"
								>
									<svg
										v-if="showPassword"
										xmlns="http://www.w3.org/2000/svg"
										width="18"
										height="18"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
									>
										<path d="M9.88 9.88a3 3 0 1 0 4.24 4.24" />
										<path
											d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"
										/>
										<path
											d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"
										/>
										<line x1="2" x2="22" y1="2" y2="22" />
									</svg>
									<svg
										v-else
										xmlns="http://www.w3.org/2000/svg"
										width="18"
										height="18"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										stroke-width="2"
										stroke-linecap="round"
										stroke-linejoin="round"
									>
										<path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
										<circle cx="12" cy="12" r="3" />
									</svg>
								</button>
							</div>
							<p
								v-if="errors.password"
								class="text-sm text-red-500 dark:text-red-400"
							>
								{{ errors.password }}
							</p>
							<div
								class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400"
							>
								<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
										clip-rule="evenodd"
									/>
								</svg>
								<span>Password must be at least 8 characters long</span>
							</div>
						</div>
					</div>
				</div>

				<!-- Step 2: Company Information -->
				<div
					v-if="currentStep === 2 && currentStepData"
					class="space-y-4 sm:space-y-6 pb-4"
				>
					<div class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<!-- Company Name -->
							<div class="space-y-2">
								<label
									for="companyName"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Company Name *
								</label>
								<input
									id="companyName"
									v-model="formData.company_name"
									type="text"
									placeholder="Enter your company name"
									class="w-full px-4 py-3 border-2 rounded-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 shadow-sm"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500/20 dark:ring-red-500/20':
											errors.company_name,
									}"
								/>
								<p
									v-if="errors.company_name"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.company_name }}
								</p>
							</div>

							<!-- Company Abbreviation -->
							<div class="space-y-2">
								<label
									for="abbr"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Abbreviation *
								</label>
								<div class="relative">
									<input
										id="abbr"
										v-model="formData.abbr"
										type="text"
										placeholder="e.g., TC"
										maxlength="5"
										class="w-full px-4 py-3 pr-12 border-2 rounded-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 shadow-sm"
										:class="{
											'border-red-500 dark:border-red-500 ring-red-500/20 dark:ring-red-500/20':
												errors.abbr,
										}"
									/>
									<button
										type="button"
										@click="generateAbbreviation(true)"
										class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 focus:outline-none"
										title="Regenerate abbreviation"
									>
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="16"
											height="16"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											stroke-width="2"
											stroke-linecap="round"
											stroke-linejoin="round"
										>
											<path
												d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"
											/>
											<path d="M21 3v5h-5" />
											<path
												d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"
											/>
											<path d="M3 21v-5h5" />
										</svg>
									</button>
								</div>
								<p
									v-if="errors.abbr"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.abbr }}
								</p>
								<div
									class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400"
								>
									<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
										<path
											fill-rule="evenodd"
											d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
											clip-rule="evenodd"
										/>
									</svg>
									<span
										>Maximum 5 characters • Click the refresh icon to
										regenerate</span
									>
								</div>
							</div>
						</div>

						<!-- Company Preferences -->
						<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
							<!-- Country -->
							<div class="space-y-2">
								<label
									for="country"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Country *
								</label>
								<select
									id="country"
									v-model="formData.country"
									class="w-full px-4 py-3 border-2 rounded-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 shadow-sm appearance-none bg-no-repeat bg-right pr-10"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500/20 dark:ring-red-500/20':
											errors.country,
									}"
								>
									<option value="">Select Country</option>
									<option
										v-for="country in countries"
										:key="country.id"
										:value="country.id"
									>
										{{ country.country_name }}
									</option>
								</select>
								<p
									v-if="errors.country"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.country }}
								</p>
							</div>

							<!-- Currency -->
							<div class="space-y-2">
								<label
									for="currency"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Currency *
								</label>
								<select
									id="currency"
									v-model="formData.default_currency"
									class="w-full px-4 py-3 border-2 rounded-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 shadow-sm appearance-none bg-no-repeat bg-right pr-10"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500/20 dark:ring-red-500/20':
											errors.default_currency,
									}"
								>
									<option value="">Select Currency</option>
									<option
										v-for="currency in currencies"
										:key="currency.id"
										:value="currency.id"
									>
										{{ currency.currency_name }} ({{ currency.symbol }})
									</option>
								</select>
								<p
									v-if="errors.default_currency"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.default_currency }}
								</p>
							</div>

							<!-- Language -->
							<div class="space-y-2">
								<label
									for="language"
									class="text-sm font-medium text-gray-700 dark:text-gray-300"
								>
									Language *
								</label>
								<select
									id="language"
									v-model="formData.language"
									class="w-full px-4 py-3 border-2 rounded-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 shadow-sm appearance-none bg-no-repeat bg-right pr-10"
									:class="{
										'border-red-500 dark:border-red-500 ring-red-500/20 dark:ring-red-500/20':
											errors.language,
									}"
								>
									<option value="">Select Language</option>
									<option
										v-for="language in languages"
										:key="language.id"
										:value="language.id"
									>
										{{ language.language_name }}
									</option>
								</select>
								<p
									v-if="errors.language"
									class="text-sm text-red-500 dark:text-red-400"
								>
									{{ errors.language }}
								</p>
							</div>
						</div>
					</div>
				</div>

				<!-- Navigation Buttons -->
				<div
					class="flex justify-between items-center mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200 dark:border-gray-700"
				>
					<!-- Previous Button -->
					<button
						v-if="currentStep > 1 && !(authStore.isLoggedIn() && currentStep === 2)"
						@click="prevStep"
						type="button"
						class="flex items-center px-4 sm:px-6 py-3 sm:py-3.5 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-xl font-medium transition-all duration-300 text-sm shadow-sm hover:shadow-md"
					>
						<svg
							class="w-4 h-4 mr-2"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M15 19l-7-7 7-7"
							/>
						</svg>
						Previous
					</button>
					<div v-else></div>

					<!-- Next/Submit Button -->
					<button
						@click="nextStep"
						type="button"
						class="flex items-center px-4 sm:px-6 py-3 sm:py-3.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-medium transition-all duration-300 text-sm shadow-lg hover:shadow-xl transform hover:scale-105"
						:disabled="isSubmitting"
					>
						<Spinner v-if="isSubmitting" size="sm" class="mr-2 text-white" />
						<span v-if="isSubmitting">
							{{
								currentStep === 1 && !authStore.isLoggedIn()
									? "Creating User..."
									: "Creating Company..."
							}}
						</span>
						<span v-else>
							{{
								currentStep === 1 && !authStore.isLoggedIn()
									? "Create User & Continue"
									: "Create Company & Complete"
							}}
						</span>
						<svg
							v-if="!isSubmitting"
							class="w-4 h-4 ml-2"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 5l7 7-7 7"
							/>
						</svg>
					</button>
				</div>

				<!-- Login Link -->
				<div class="text-center mt-6 sm:mt-8">
					<p class="text-sm sm:text-base text-gray-600 dark:text-gray-400">
						Already have an account?
						<router-link
							to="/dashboard/login"
							class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200 hover:underline"
						>
							Sign in
						</router-link>
					</p>
				</div>
			</div>
		</div>

		<!-- Fallback Loading State -->
		<LoadingState
			v-else
			:message="`Initializing form... (Debug: isLoadingData=${isLoadingData}, steps=${
				steps?.length || 0
			})`"
		/>
	</div>
</template>
