import { createRouter, createWebHistory } from "vue-router";
import DashboardLayout from "@/layouts/DashboardLayout.vue";
import { useAuthStore } from "../stores/auth";

const routes = [
	{ path: "/", redirect: "/dashboard" },
	{
		path: "/login",
		redirect: "/dashboard/login",
	},
	{
		path: "/dashboard/login",
		name: "login",
		component: () => import("../views/LoginView.vue"),
		meta: {
			requiresAuth: false,
			guestOnly: true,
		},
	},
	{
		path: "/dashboard/register",
		name: "register",
		component: () => import("../views/RegistrationView.vue"),
		meta: {
			requiresAuth: false,
			guestOnly: true,
		},
	},
	{
		path: "/dashboard",
		component: DashboardLayout,
		meta: {
			requiresAuth: true,
			requiresCompany: true,
		},
		children: [
			{
				path: "",
				name: "dashboard",
				component: () => import("@/views/Dashboard/DashboardIndex.vue"),
			},
			// Products
			{
				path: "products",
				name: "products",
				component: () => import("@/views/Products/ProductList.vue"),
			},
			{
				path: "products/new",
				name: "new-product",
				component: () => import("@/views/Products/NewProduct.vue"),
			},
			// Categories
			{
				path: "products/:id",
				name: "product-details",
				component: () => import("@/views/Products/ProductDetails.vue"),
			},
			// Categories
			{
				path: "categories",
				name: "categories",
				component: () => import("@/views/Categories/CategoryList.vue"),
			},
			{
				path: "categories/new",
				name: "new-category",
				component: () => import("@/views/Categories/NewCategory.vue"),
			},
			{
				path: "categories/:id",
				name: "category-details",
				component: () => import("@/views/Categories/CategoryDetails.vue"),
			},
			// Collections
			{
				path: "collections",
				name: "collections",
				component: () => import("@/views/Collections/CollectionList.vue"),
			},
			{
				path: "collections/new",
				name: "new-collection",
				component: () => import("@/views/Collections/NewCollection.vue"),
			},
			{
				path: "collections/:id",
				name: "collection-details",
				component: () => import("@/views/Collections/CollectionDetails.vue"),
			},

			// Orders
			{
				path: "orders",
				name: "orders",
				component: () => import("@/views/Orders/OrderList.vue"),
			},
			{
				path: "orders/new",
				name: "new-order",
				component: () => import("@/views/Orders/OrderCreate.vue"),
			},
			{
				path: "orders/:id",
				name: "order-details",
				component: () => import("@/views/Orders/OrderDetail.vue"),
			},
			{
				path: "orders/:id/edit",
				name: "edit-order",
				component: () => import("@/views/Orders/OrderEdit.vue"),
			},
			{
				path: "orders/:id/edit-products",
				name: "edit-order-products",
				component: () => import("@/views/Orders/OrderEditProducts.vue"),
			},
			// Invoices
			{
				path: "invoices",
				name: "invoices",
				component: () => import("@/views/Invoices/InvoiceList.vue"),
			},
			{
				path: "invoices/new",
				name: "new-invoice",
				component: () => import("@/views/Invoices/NewInvoice.vue"),
			},
			{
				path: "invoices/:id",
				name: "invoice-details",
				component: () => import("@/views/Invoices/InvoiceDetail.vue"),
			},
			// Payments
			{
				path: "payments",
				name: "payments",
				component: () => import("@/views/Payments/PaymentList.vue"),
			},
			{
				path: "payments/new",
				name: "new-payment",
				component: () => import("@/views/Payments/PaymentForm.vue"),
			},
			{
				path: "payments/:id",
				name: "payment-details",
				component: () => import("@/views/Payments/PaymentDetail.vue"),
			},
			// Payment Methods
			{
				path: "payment-methods",
				name: "payment-methods",
				component: () => import("@/views/PaymentMethods/PaymentMethodList.vue"),
			},
			// Inventory
			{
				path: "inventory",
				name: "inventory",
				component: () => import("@/views/Inventory/InventoryList.vue"),
			},
			{
				path: "inventory/dashboard",
				name: "inventory-dashboard",
				component: () => import("@/views/Inventory/InventoryDashboard.vue"),
			},
			// Reports
			{
				path: "reports/sales",
				name: "sales-report",
				component: () => import("@/views/Reports/SalesReport.vue"),
			},
			// Customers
			{
				path: "customers",
				name: "customers",
				component: () => import("@/views/Customers/CustomerList.vue"),
			},
			{
				path: "customers/:id",
				name: "customer-details",
				component: () => import("@/views/Customers/CustomerDetails.vue"),
			},
			{
				path: "customers/new",
				name: "new-customer",
				component: () => import("@/views/Customers/NewCustomer.vue"),
			},
			{
				path: "customer-groups",
				name: "customer-groups",
				component: () => import("@/views/CustomerGroups/CustomerGroupList.vue"),
			},
			{
				path: "customer-groups/:id",
				name: "customer-group-details",
				component: () => import("@/views/CustomerGroups/CustomerGroupDetails.vue"),
			},
			{
				path: "customer-groups/new",
				name: "new-customer-groups",
				component: () => import("@/views/CustomerGroups/NewCustomerGroup.vue"),
			},
			// Selling Channels
			{
				path: "selling-channels",
				name: "selling-channels",
				component: () => import("@/views/SellingChannels/ChannelsList.vue"),
			},
			{
				path: "selling-channels/setup/vue-storefront",
				name: "vue-storefront-setup",
				component: () => import("@/views/SellingChannels/VueStorefrontSetup.vue"),
			},
			{
				path: "selling-channels/setup/pos-awesome",
				name: "pos-awesome-setup",
				component: () => import("@/views/SellingChannels/POSAwesomeSetup.vue"),
			},
			// Add this to the children array of the dashboard path
			{
				path: "profile",
				name: "user-profile",
				component: () => import("@/views/Profile/UserProfile.vue"),
			},
		],
	},
	{
		path: "/:pathMatch(.*)*",
		name: "not-found",
		component: () => import("../views/NotFoundView.vue"),
	},
];

const router = createRouter({
	history: createWebHistory(""),
	routes,
});

// Navigation guard
router.beforeEach(async (to, from, next) => {
	const authStore = useAuthStore();

	try {
		const isLoggedIn = authStore.isLoggedIn();
		let hasCompany = false;

		// Only check company status for authenticated users
		if (isLoggedIn) {
			try {
				hasCompany = await authStore.checkHasCompany();
			} catch (error) {
				console.warn("Failed to check company status, assuming no company:", error);
				hasCompany = false;
			}
		}

		// Redirect authenticated users away from guest-only pages, but allow register page for users without company
		if (to.matched.some((record) => record.meta.guestOnly) && isLoggedIn) {
			// Allow authenticated users to access register page if they don't have a company
			if (to.name === "register" && !hasCompany) {
				// Allow access to register page for company creation
			} else {
				console.log(
					"Authenticated user trying to access guest-only page, redirecting to dashboard"
				);
				return next({ name: "dashboard" });
			}
		}

		// Redirect users without a company to onboarding
		if (
			to.matched.some((record) => record.meta.requiresCompany) &&
			!hasCompany &&
			to.name !== "register"
		) {
			console.log("User trying to access page without a company, redirecting to onboarding");
			return next({ name: "register" });
		}

		// Redirect users with a company away from register page to dashboard
		if (to.name === "register" && isLoggedIn && hasCompany) {
			console.log(
				"User with company trying to access register page, redirecting to dashboard"
			);
			return next({ name: "dashboard" });
		}

		// Clear auth data only for guest-only pages when user is not logged in
		if ((to.name === "login" || to.name === "register") && !isLoggedIn) {
			authStore.clearAuthData();
		}

		// Check if route requires authentication
		if (to.matched.some((record) => record.meta.requiresAuth)) {
			if (!isLoggedIn) {
				console.log(
					"Unauthenticated user trying to access protected page, redirecting to login"
				);
				return next({ name: "login" });
			}
		}
	} catch (err) {
		console.error("Auth check failed:", err);
		return next({ name: "login" });
	}

	next();
});

export default router;
