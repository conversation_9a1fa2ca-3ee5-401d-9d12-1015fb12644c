import { reactive } from "vue";
import authService from "../services/authService";

const storedUser = JSON.parse(localStorage.getItem("user") || "null");

const state = reactive({
	user: storedUser,
	isAuthenticated: !!storedUser,
	sessionChecked: false,
	loginAttempt: false,
	hasCompany: false,
});

const actions = {
	async login(email, password) {
		try {
			const response = await authService.login(email, password);

			if (response.success && response.data) {
				const user = response.data.user || response.data;
				state.user = user;
				localStorage.setItem("user", JSON.stringify(user));
				state.isAuthenticated = true;
				state.sessionChecked = true;
				return true;
			}
			actions.clearAuthData();
			return false;
		} catch (error) {
			console.error("Auth store: Login error:", error);
			actions.clearAuthData();
			throw error;
		}
	},

	async logout() {
		try {
			await authService.logout();
		} catch (error) {
			console.error("Auth store: Logout error (API call failed):", error);
		} finally {
			actions.clearAuthData();
		}
	},

	async checkAuth() {
		if (state.sessionChecked) return state.isAuthenticated;

		try {
			const { isAuthenticated, user } = await authService.checkAuthStatus();
			if (isAuthenticated && user) {
				const userInfo = {
					name: user.user,
					email: user.user,
					user_type: user.user_type,
					full_name: user.user,
				};

				state.user = userInfo;
				localStorage.setItem("user", JSON.stringify(userInfo));
				state.isAuthenticated = true;
			} else {
				actions.clearAuthData();
			}
		} catch (error) {
			console.error("Auth store: Check auth failed", error);
			actions.clearAuthData();
		} finally {
			state.sessionChecked = true;
		}
		return state.isAuthenticated;
	},
	async checkHasCompany() {
		try {
			const { isAuthenticated, user } = await authService.checkAuthStatus();
			if (isAuthenticated && user) {
				const response = await authService.checkHasCompany();
				state.hasCompany = response.data.has_company;
				return response.data.has_company;
			}
		} catch (error) {
			console.error("Auth store: Check has company failed", error);
			return false;
		}
	},

	clearAuthData() {
		state.user = null;
		state.isAuthenticated = false;
		localStorage.removeItem("user");
	},

	setAuthData(authData) {
		const user = authData.user;
		const userInfo = {
			name: user.user_id || user.email,
			email: user.email,
			user_type: user.user_roles || "Authenticated User",
			full_name: user.full_name,
			first_name: user.first_name,
			last_name: user.last_name,
		};

		state.user = userInfo;
		localStorage.setItem("user", JSON.stringify(userInfo));
		state.isAuthenticated = true;
		state.sessionChecked = true;
	},
};

export function useAuthStore() {
	const isLoggedIn = () => state.isAuthenticated;

	return {
		...state,

		isLoggedIn,

		userData: () => state.user,
		hasCheckedSession: () => state.sessionChecked,

		setLoginAttempt: (value) => {
			state.loginAttempt = value;
		},
		hasLoginAttempt: () => state.loginAttempt,
		hasCompany: () => state.hasCompany,

		...actions,
	};
}
