import { reactive } from "vue";
import authService from "../services/authService";

const storedUser = JSON.parse(localStorage.getItem("user") || "null");

const state = reactive({
	user: storedUser,
	isAuthenticated: !!storedUser,
	sessionChecked: false,
	loginAttempt: false,
	hasCompany: false,
	companyChecked: false,
	companyCheckInProgress: false,
	lastCompanyCheck: 0,
});

// Minimum time between company checks (in milliseconds)
const COMPANY_CHECK_COOLDOWN = 2000; // 2 seconds

const actions = {
	async login(email, password) {
		try {
			const response = await authService.login(email, password);

			if (response.success && response.data) {
				const user = response.data.user || response.data;
				state.user = user;
				localStorage.setItem("user", JSON.stringify(user));
				state.isAuthenticated = true;
				state.sessionChecked = true;

				// Note: Company status will be checked by router navigation guard

				return true;
			}
			actions.clearAuthData();
			return false;
		} catch (error) {
			console.error("Auth store: Login error:", error);
			actions.clearAuthData();
			throw error;
		}
	},

	async logout() {
		try {
			await authService.logout();
		} catch (error) {
			console.error("Auth store: Logout error (API call failed):", error);
		} finally {
			actions.clearAuthData();
		}
	},

	async checkAuth() {
		if (state.sessionChecked) return state.isAuthenticated;

		try {
			const { isAuthenticated, user } = await authService.checkAuthStatus();
			if (isAuthenticated && user) {
				const userInfo = {
					name: user.user,
					email: user.user,
					user_type: user.user_type,
					full_name: user.user,
				};

				state.user = userInfo;
				localStorage.setItem("user", JSON.stringify(userInfo));
				state.isAuthenticated = true;
			} else {
				actions.clearAuthData();
			}
		} catch (error) {
			console.error("Auth store: Check auth failed", error);
			actions.clearAuthData();
		} finally {
			state.sessionChecked = true;
		}
		return state.isAuthenticated;
	},
	async checkHasCompany() {
		// Return cached result if already checked
		if (state.companyChecked) {
			return state.hasCompany;
		}

		// Check cooldown to prevent rapid successive calls
		const now = Date.now();
		if (now - state.lastCompanyCheck < COMPANY_CHECK_COOLDOWN) {
			console.log("Company check skipped due to cooldown");
			return state.hasCompany;
		}

		// Prevent multiple simultaneous calls
		if (state.companyCheckInProgress) {
			// Wait for the ongoing check to complete
			while (state.companyCheckInProgress) {
				await new Promise(resolve => setTimeout(resolve, 100));
			}
			return state.hasCompany;
		}

		try {
			state.companyCheckInProgress = true;
			state.lastCompanyCheck = now;

			// First check if user is authenticated using local state
			if (!state.isAuthenticated) {
				console.log("User not authenticated, skipping company check");
				state.hasCompany = false;
				state.companyChecked = true;
				return false;
			}

			// Try to check auth status from server
			let isAuthenticated = false;
			let user = null;

			try {
				const authStatus = await authService.checkAuthStatus();
				isAuthenticated = authStatus.isAuthenticated;
				user = authStatus.user;
			} catch (authError) {
				console.warn("Auth status check failed, using local state:", authError);
				// Fall back to local authentication state
				isAuthenticated = state.isAuthenticated;
				user = state.user;
			}

			if (isAuthenticated && user) {
				const response = await authService.checkHasCompany();
				state.hasCompany = response.has_company;
				state.companyChecked = true;
				return response.has_company;
			}

			// User not authenticated
			state.hasCompany = false;
			state.companyChecked = true;
			return false;
		} catch (error) {
			console.error("Auth store: Check has company failed", error);
			// On error, assume no company to prevent blocking user flow
			state.hasCompany = false;
			state.companyChecked = true; // Mark as checked to prevent retry loops
			return false;
		} finally {
			state.companyCheckInProgress = false;
		}
	},

	async refreshCompanyStatus() {
		// Force refresh by clearing cache and resetting cooldown
		state.companyChecked = false;
		state.lastCompanyCheck = 0;
		return await actions.checkHasCompany();
	},

	clearAuthData() {
		state.user = null;
		state.isAuthenticated = false;
		state.hasCompany = false;
		state.companyChecked = false;
		state.companyCheckInProgress = false;
		state.lastCompanyCheck = 0;
		localStorage.removeItem("user");
	},

	async setAuthData(authData) {
		const user = authData.user;
		const userInfo = {
			name: user.user_id || user.email,
			email: user.email,
			user_type: user.user_roles || "Authenticated User",
			full_name: user.full_name,
			first_name: user.first_name,
			last_name: user.last_name,
		};

		state.user = userInfo;
		localStorage.setItem("user", JSON.stringify(userInfo));
		state.isAuthenticated = true;
		state.sessionChecked = true;

		// Note: Company status will be checked by router navigation guard
	},
};

export function useAuthStore() {
	const isLoggedIn = () => state.isAuthenticated;

	return {
		...state,

		isLoggedIn,

		userData: () => state.user,
		hasCheckedSession: () => state.sessionChecked,

		setLoginAttempt: (value) => {
			state.loginAttempt = value;
		},
		hasLoginAttempt: () => state.loginAttempt,
		hasCompany: () => state.hasCompany,

		// Helper method to check if user appears to be authenticated based on local data
		hasLocalAuthData: () => {
			return !!(state.user && state.isAuthenticated && localStorage.getItem("user"));
		},

		...actions,
	};
}
