import axios from "axios";
import type { AxiosInstance, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from "axios";
import { useAuthStore } from "@/stores/auth";
import cookieUtils from "../utils/cookieUtils";
// @ts-ignore - Assuming router is a JS file and type declarations might be missing
import router from "@/router";

// Types
interface LoginResponse {
  message: string;
  full_name: string;
  home_page: string;
  redirect_to: string;
}

interface AuthStatusResponse {
  user: string;
  user_type: string;
}

interface LoginResult {
  success: boolean;
  message: string;
  data?: {
    user: {
      full_name: string;
      email: string;
      home_page: string;
      redirect_to: string;
    };
  };
}

// CSRF token management - use global token first, server as fallback
class CSRFTokenManager {
  private cache: string | null = null;
  private expiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  async getToken(): Promise<string | null> {
    const now = Date.now();
    
    // Return cached token if still valid
    if (this.cache && now < this.expiry) {
      return this.cache;
    }

    // Try to get from global window variable first (most efficient)
    const globalToken = cookieUtils.getCSRFToken();
    if (globalToken) {
      this.cache = globalToken;
      this.expiry = now + this.CACHE_DURATION;
      return this.cache;
    }

    // Fallback to server endpoint if global token not available
    try {
      const csrfClient = axios.create({
        withCredentials: true,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });
      
      const response = await csrfClient.get<{ data: { csrf_info: { csrf_token: string } } }>('/api/v1/utilities/csrf-token');
      const token = response.data?.data?.csrf_info?.csrf_token;
      
      if (token) {
        this.cache = token;
        this.expiry = now + this.CACHE_DURATION;
        return this.cache;
      }
    } catch (error: any) {
      // Don't log warnings for 401 errors during registration
      if (error.response?.status !== 401) {
        console.warn('Failed to fetch CSRF token from server:', error);
      }
    }

    return null;
  }

  clearCache(): void {
    this.cache = null;
    this.expiry = 0;
  }
}

// API client configuration
const apiClient: AxiosInstance = axios.create({
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

const csrfManager = new CSRFTokenManager();

// Request interceptor for CSRF tokens
apiClient.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
  const isMutatingRequest = ['post', 'put', 'delete', 'patch'].includes(config.method?.toLowerCase() || '');
  
  // Skip CSRF token for registration endpoints (they're public)
  const isUserCreationEndpoint = config.url?.includes('/api/v1/onboarding/create-user')
  
  // Only fetch CSRF token for mutating requests that actually need it
  if (isMutatingRequest && !isUserCreationEndpoint) {
    try {
      const csrfToken = await csrfManager.getToken();
      if (csrfToken) {
        config.headers['X-Frappe-CSRF-Token'] = csrfToken;
      }
    } catch (error: any) {
      // Don't log warnings for 401 errors during registration
      if (error.response?.status !== 401) {
        console.warn('Error getting CSRF token:', error);
      }
    }
  }
  
  return config;
});

// Response interceptor for auth errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    if (error.response?.status === 401 || error.response?.status === 403) {
      const authStore = useAuthStore();
      authStore.clearAuthData();
      
      // Clear CSRF cache on auth errors
      csrfManager.clearCache();
      
      // Redirect to login if not already there
      setTimeout(() => {
        if (router.currentRoute.value.name !== 'login') {
          router.push({ name: 'login' });
        }
      }, 100);
    }
    return Promise.reject(error);
  }
);

// Main auth service
export const authService = {
  /**
   * Login user with email and password
   */
  async login(email: string, password: string): Promise<LoginResult> {
    try {
      // Input validation
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      const formData = new FormData();
      formData.append('cmd', 'login');
      formData.append('usr', email.trim());
      formData.append('pwd', password);

      const response: AxiosResponse<LoginResponse> = await apiClient.post(
        "/api/method/login",
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (response.status === 200 && response.data.message === "Logged In") {
        // Save session data
        const sessionData = {
          email: email.trim(),
          full_name: response.data.full_name,
          home_page: response.data.home_page,
          redirect_to: response.data.redirect_to,
          login_time: new Date().toISOString()
        };
        cookieUtils.saveSessionData(sessionData);
        
        return {
          success: true,
          message: "Login successful",
          data: {
            user: {
              full_name: response.data.full_name,
              email: email.trim(),
              home_page: response.data.home_page,
              redirect_to: response.data.redirect_to,
            },
          },
        };
      } else {
        throw new Error(response.data.message || "Login failed");
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      } else if (error.request) {
        throw new Error("No response from server. Please try again later.");
      } else {
        throw new Error(error.message || "An error occurred during login");
      }
    }
  },

  /**
   * Logout user
   */
  async logout(): Promise<boolean> {
    try {
      await apiClient.post("/logout", {}, {
        baseURL: "/api/method",
      });
      
      // Clear all session data
      cookieUtils.clearAllCookies();
      csrfManager.clearCache();
      
      return true;
    } catch (error) {
      console.error("Logout error:", error);
      // Still clear local data even if server logout fails
      cookieUtils.clearAllCookies();
      csrfManager.clearCache();
      return false;
    }
  },

  /**
   * Check authentication status
   */
  async checkAuthStatus(): Promise<{ isAuthenticated: boolean; user?: AuthStatusResponse }> {
    try {
      const response = await apiClient.get<{ message: AuthStatusResponse }>("/frappe.realtime.get_user_info", {
        baseURL: "/api/method",
      });
      
      const user = response.data?.message;
      const isAuthenticated = user && user.user !== "Guest";
      
      return { 
        isAuthenticated, 
        user: isAuthenticated ? user : undefined 
      };
    } catch (error) {
      console.error("Auth status check failed:", error);
      return { isAuthenticated: false };
    }
  },

  async checkHasCompany(): Promise<boolean> {
    try {
      const response = await apiClient.get<{ has_company: boolean }>("/api/v1/utilities/user-company-exists");
      return response.data.has_company;
    } catch (error) {
      console.error("Company check failed:", error);
      return false;
    }
  },

  /**
   * Get API client instance
   */
  getApiClient(): AxiosInstance {
    return apiClient;
  },

  /**
   * Get CSRF token (for external use)
   */
  async getCSRFToken(): Promise<string | null> {
    return csrfManager.getToken();
  }
};

export default authService;